from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import numpy as np
import logging

logger = logging.getLogger(__name__)

class QdrantService:
    def __init__(self):
        try:
            # Fix SSL certificate issue by setting environment variable
            import os
            os.environ.pop('SSL_CERT_FILE', None)  # Remove if exists

            self.client = QdrantClient(url="http://localhost:6333")
            self.collection_name = "face_embeddings"
            self.vector_size = 512  # Adjust based on your face encoding size
            self._ensure_collection_exists()
            self.available = True
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {str(e)}")
            self.available = False
            self.client = None

    def _ensure_collection_exists(self):
        """Create collection if it doesn't exist"""
        if not self.client:
            logger.warning("Qdrant client not available, skipping collection check")
            return

        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection '{self.collection_name}'")
            else:
                logger.info(f"Collection '{self.collection_name}' already exists")
        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {str(e)}")
            self.available = False

    def add_face_embedding(self, user_id, username, encoding):
        """Add face embedding to Qdrant"""
        if not self.client or not self.available:
            logger.warning("Qdrant client not available, skipping add_face_embedding")
            return False

        try:
            print("Adding face embedding...")
            print(f"User ID type: {type(user_id)}, value: {user_id}")

            # Generate a UUID for the point ID
            # Qdrant requires point IDs to be either an unsigned integer or a UUID
            import uuid
            import time

            # Generate a unique UUID for each embedding
            # This ensures we add a new embedding rather than replacing an existing one
            # We'll use a combination of user_id and current timestamp to make it unique
            current_time = int(time.time())
            namespace = uuid.NAMESPACE_DNS
            name = f"user_{user_id}_{current_time}"
            point_id = str(uuid.uuid5(namespace, name))

            print(f"Using unique UUID point ID: {point_id}")

            # Convert numpy array to list if needed
            if isinstance(encoding, np.ndarray):
                encoding = encoding.tolist()

            # Ensure encoding is a list
            if not isinstance(encoding, list):
                print(f"Warning: encoding is not a list, type: {type(encoding)}")
                try:
                    encoding = list(encoding)
                except:
                    print(f"Failed to convert encoding to list, using as is")

            point = PointStruct(
                id=point_id,  # Use unique UUID
                vector=encoding,
                payload={
                    "user_id": user_id,
                    "username": username,
                    "timestamp": current_time  # Add timestamp to payload for reference
                }
            )

            print(f"Upserting point with UUID: {point_id}")
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            logger.info(f"Added additional face embedding for user {username} (ID: {user_id}) to Qdrant with UUID: {point_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add face embedding: {str(e)}")
            # Print the raw error for debugging
            import traceback
            print(f"Exception details: {traceback.format_exc()}")
            self.available = False
            return False

    def search(self, collection_name, query_vector, top_k=5):
        if not self.client or not self.available:
            logger.warning("Qdrant client not available, skipping search")
            return []

        try:
            return self.client.search(
                    collection_name=collection_name,
                    query_vector=query_vector,
                    limit=top_k
                )
        except Exception as e:
            logger.error(f"Failed to search in Qdrant: {str(e)}")
            self.available = False
            return []

    def delete_face_embedding(self, user_id):
        """Delete all face embeddings from Qdrant for a specific user_id"""
        if not self.client or not self.available:
            logger.warning("Qdrant client not available, skipping delete_face_embedding")
            return False

        try:
            print(f"Deleting all face embeddings for user_id: {user_id}, type: {type(user_id)}")

            # Delete by filter on the payload's user_id field
            # This will delete all embeddings for this user, regardless of the point_id
            from qdrant_client.models import Filter, FieldCondition, MatchAny

            # Convert user_id to string if it's not already
            user_id_str = str(user_id)

            # Create a filter to match all points with this user_id (as either string or integer)
            # This handles cases where some embeddings might have been stored with integer user_id
            # and others with string user_id
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="user_id",
                        match=MatchAny(any=[user_id_str, user_id])
                    )
                ]
            )

            # Delete all points that match the filter
            result = self.client.delete(
                collection_name=self.collection_name,
                points_selector=filter_condition
            )

            deleted_count = result.status.deleted_count if hasattr(result, 'status') and hasattr(result.status, 'deleted_count') else 0
            logger.info(f"Deleted {deleted_count} face embeddings for user_id: {user_id} (String: {user_id_str}) from Qdrant")
            return True
        except Exception as e:
            logger.error(f"Failed to delete face embeddings: {str(e)}")
            # Print the raw error for debugging
            import traceback
            print(f"Exception details: {traceback.format_exc()}")
            self.available = False
            return False