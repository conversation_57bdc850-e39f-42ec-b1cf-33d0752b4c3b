import cv2
import threading
from ultralytics import YOLO
import numpy as np
from fastapi import WebSocketDisconnect, WebSocket
import asyncio
from collections import deque
import json
import logging
import queue


# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('CrowdDetection')

class Crowd_Detection:
    def __init__(self, model_path,conf_value,alert_system=None):
        logger.info("Initializing Crowd Detection")
        self.conf_value = conf_value
        self.model = YOLO(model_path,task="detect")  # Load YOLO model
        self.running = False
        self.threads = []
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (480, 320))

        # Store alert system reference
        self.alert_system = alert_system
        self.active = False
        
        # Initialize settings only if alert system is provided
        if self.alert_system:
            self.settings = self.alert_system.load_settings()
            self.active = self.settings.get("active", False)
            logger.info(f"Alert system initialized. Active: {self.active}")
        else:
            logger.warning("No alert system provided")


    def start(self, camera_details, alert_system=None):
        logger.info("Starting crowd detection")

        if self.alert_system:
            self.settings = self.alert_system.load_settings()
            self.active = self.settings.get("active", False)
            logger.info(f"Using alert system. Active: {self.active}")

        self.camera_name = []
        self.rtsp_url = []
        self.roi_coordinates = []
        self.cap_devices = []

        dic = camera_details


        
        for key,value in dic.items():
            self.camera_name.append(key)
            self.roi_coordinates.append(value[1])

            if value[0].isdigit():
                value = int(value[0])
                self.rtsp_url.append(value)
                cap = cv2.VideoCapture(value)  #,cv2.CAP_FFMPEG
                # Increase buffer size
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                if cap.isOpened():
                    self.cap_devices.append(cap)
                else:
                    self.cap_devices.append(None)
                
            else:
                cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)   #,cv2.CAP_FFMPEG
                # Increase buffer size
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                if cap and cap.isOpened():
                    self.cap_devices.append(cap)  
                else:
                    self.cap_devices.append(None)


        self.frames = [None] * len(self.cap_devices)  # One frame for each camera
        self.total_people_counts = [None] * len(self.cap_devices)
        self.roi_count = [None] * len(self.cap_devices)

        if not self.running:
            self.running = True
            self.rois = self.roi_coordinates
            self.preprocessed_rois = []
            for camera_rois in self.rois:
                processed = [np.array(roi, dtype=np.int32) for roi in camera_rois]
                self.preprocessed_rois.append(processed)

            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap))
                    thread.daemon = True
                    thread.start()

                else:        #TO DISPLAY THE DEVICE IS OFFLINE
                    
                    temp = cv2.putText(self.blank,f"{self.camera_name[idx]} is Offline",(30,250),fontFace=cv2.FONT_HERSHEY_SIMPLEX,fontScale=1,thickness=3,color=(255,255,255))
                    _, temp = cv2.imencode('.jpg', temp)
                    # self.frames[idx] = base64.b64encode(temp).decode('utf-8')
                    self.frames[idx] = temp.tobytes()
    def stop(self):
        logger.info("Stopping crowd detection")
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        for thread in self.threads:
            thread.join()
        self.threads = []
        self.frames = [None] * len(self.cap_devices)  # Reset frames
        logger.info("Crowd detection stopped")

    def find_polygon_centroid(self,coordinates):
            n = len(coordinates)
            
            # Initialize variables
            A = 0  # Area
            C_x = 0
            C_y = 0
            
            # Calculate area and centroid components
            for i in range(n - 1):
                x_i, y_i = coordinates[i]
                x_next, y_next = coordinates[i + 1]
                cross_product = (x_i * y_next) - (x_next * y_i)
                A += cross_product
                C_x += (x_i + x_next) * cross_product
                C_y += (y_i + y_next) * cross_product
            
            A *= 0.5
            C_x /= (6 * A)
            C_y /= (6 * A)
            
            return (int(C_x), int(C_y))

    def process_frame(self, img):
        boxes = []
        # confidences = []
        results = self.model(img, stream=True,classes=[0],verbose=False)
        for result in results:
            for box in result.boxes:
                conf = box.conf[0]
                class_id = int(box.cls[0])
                if class_id == 0 and conf > self.conf_value:  # 0 corresponds to people
                    x,y,x1,y1=map(int,box.xyxy[0])
                    boxes.append([x,y,x1,y1])    
                    # confidences.append(float(conf))
                    
        return boxes

    def count_persons_in_rois(self, boxes, idx, frame):
        """Count the number of persons inside each ROI."""
        roi_person_counts = [0] * len(self.rois[idx])

        for i, box in enumerate(boxes):
            x, y, x1, y1 = box
            cv2.rectangle(frame, (x,y), (x1,y1), (0,0,255), 2)
            person_center = (((x1- x) // 2)+x, ((y1 - y) // 2)+y)
            
            # Check if the center of the person is inside any ROI
            for j, roi in enumerate(self.rois[idx]):
                roi_array = np.array(roi, dtype=np.float32)
                if cv2.pointPolygonTest(np.array(roi_array), person_center, False) >= 0:
                    roi_person_counts[j] += 1


        return roi_person_counts


    def update(self, idx, cap):
        frame_counter,skip_frames = 0, 2
        while self.running:
            ret, frame = cap.read()
            if not ret:
                break
            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue   

            frame = cv2.resize(frame, (640, 480),interpolation=cv2.INTER_AREA)  # Reduced resolution

            boxes = self.process_frame(frame)
            self.total_people_counts[idx] = len(boxes)

            if self.preprocessed_rois:
                for i, _roi in enumerate(self.preprocessed_rois[idx]):
                    cv2.polylines(frame, [np.array(_roi, dtype=np.int32)], isClosed=True, color=(0, 255, 0), thickness=2)
                    centroid_coordinate = self.find_polygon_centroid(_roi)
                    cv2.putText(frame, f"ROI:{i+1}", centroid_coordinate, cv2.FONT_HERSHEY_SIMPLEX, 1, color=(0, 0, 255), thickness=1)

                roi_person_counts = self.count_persons_in_rois(boxes, idx, frame)
                self.roi_count[idx] = roi_person_counts

            # Check for alerts after counting all people in all regions
            if self.alert_system and self.active:
                for j, count in enumerate(roi_person_counts):
                    if count > 0:  # Only alert if there are people in the region
                        logger.debug(f"Checking alert for camera {self.camera_name[idx]}, region {j}, count {count}")
                        self.alert_system.check_and_alert(
                            self.camera_name[idx], j, count
                        )

            frame = cv2.resize(frame, (480, 320))
            _, buffer = cv2.imencode('.png', frame) # Compress frame
            self.frames[idx] = buffer.tobytes()

            if not self.running:
                break
            





class CameraWebSocketHandler:
    def __init__(self):
        self.is_streaming = False
        self.capture = None
        self.frame_buffer = deque(maxlen=5)  # Prevents frame lag
        self.current_camera_index = 0
        self.camera_name = []
        self.rtsp_url = []
        self.roi_coordinates = []

    def start(self, camera_details):
        self.camera_name = []
        self.rtsp_url = []
        self.roi_coordinates = []

        for key, value in camera_details.items():
            self.camera_name.append(key)
            self.roi_coordinates.append(value[1])
            self.rtsp_url.append(int(value[0]) if value[0].isdigit() else value[0])

        # Initialize Video Capture
        self.capture = cv2.VideoCapture(self.rtsp_url[self.current_camera_index],cv2.CAP_FFMPEG)
        self.capture.set(cv2.CAP_PROP_BUFFERSIZE, 2)  # Reduce buffering delay
        self.is_streaming = self.capture.isOpened()

    def stop(self):
        """Stop video streaming."""
        if self.capture:
            self.capture.release()
        self.is_streaming = False

    def change_camera(self, action):
        """Change the current camera based on the action ('next' or 'prev')."""
        if action == "next":
            self.current_camera_index = (self.current_camera_index + 1) % len(self.rtsp_url)
        elif action == "prev":
            self.current_camera_index = (self.current_camera_index - 1) % len(self.rtsp_url)
        else:
            raise ValueError("Invalid action. Use 'next' or 'prev'.")

        # Stop the current capture
        if self.capture:
            self.capture.release()

        # Start capturing from the new camera
        self.capture = cv2.VideoCapture(self.rtsp_url[self.current_camera_index],cv2.CAP_FFMPEG)
        self.capture.set(cv2.CAP_PROP_BUFFERSIZE, 2)  # Reduce buffering delay
        self.is_streaming = self.capture.isOpened()

    async def stream_video(self, websocket: WebSocket):
        """Stream video frames over WebSocket."""
        await websocket.accept()
        try:
            while self.is_streaming:
                if not self.capture.isOpened():
                    await asyncio.sleep(0.5)
                    continue

                ret, frame = self.capture.read()
                if not ret or frame is None:
                    await asyncio.sleep(0.1)
                    continue

                # Resize and compress efficiently
                frame = cv2.resize(frame, (640, 480), interpolation=cv2.INTER_AREA)
                _, buffer = cv2.imencode(
                    ".jpg", frame, [cv2.IMWRITE_JPEG_QUALITY, 80]
                )

                # Convert to bytes
                binary_frame = buffer.tobytes()

                # Send metadata + frame in one message
                metadata = json.dumps({"camera_name": self.camera_name[self.current_camera_index]})
                await websocket.send_bytes(metadata.encode() + b"\n" + binary_frame)

                await asyncio.sleep(0.033)  # Approx 30 FPS

        except WebSocketDisconnect:
            print("WebSocket disconnected")
        finally:
            self.stop()