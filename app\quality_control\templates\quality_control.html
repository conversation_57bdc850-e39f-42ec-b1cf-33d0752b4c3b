<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Quality Control Dashboard</title>
  <link rel="stylesheet" href="{{ url_for('quality_control_static', path='quality_control.css') }}">
</head>
<body>

  <div class="container">
    <div class="sidebar">
      <div>
        <div class="logo">
          <img src="/static/logo.png" alt="Logo"><br><br><br><br><br>
        </div>
        <div class="buttons">
          <button class="waterlevel-btn" id="waterlevelBtn">Recommended WaterLevel</button>

          <button class="menu-btn" id="startStreamBtn">Start Live Cam Quality Control</button>
          <button class="menu-btn" id="stopStreamBtn">Stop Live Cam Quality Control</button>     
          <!-- <button class="addcamera-btn" id="managecamera">Manage Camera</button> -->
        
        </div>
      </div>
      
          <button class="homepage-btn" id="homepageBtn">Go to Homepage</button>
    </div>

    <div class="main">
      <div class="webcam-section" id="webcamSection">
        <h2>Webcam</h2>
      </div><br><br>
      <div class="video-grid" id="feeds">
        <img id="webcamVideo" width="720" height="600">
      </div>
    </div>

    <div class="live-observations">
      <h2>Live Observations</h2>
      <ul id="observationsList">
        <li><strong>Bottle:</strong> <span id="bottleStatus">N/A</span></li>
        <li><strong>Cap:</strong> <span id="capStatus">N/A</span></li>
        <li><strong>WaterLevel:</strong> <span id="waterLevelStatus">N/A</span></li>
      </ul>
    </div>
  </div>


  <!-- Water Level Modal -->
  <div id="waterLevelModal" class="modal">
    <div class="modal-content waterlevel-modal">
      <span class="close-btn" id="closeWaterLevelModal">&times;</span>
      <h2 class="modal-title">Recommended Water Level</h2>
      
      <div class="modal-body">
        <p>Please enter the recommended water level below:</p>
        <input type="number" id="recommendedWaterLevel" name="recommendedWaterLevel" placeholder="Enter Water Level" required>
      </div>

      <div class="modal-footer">
        <button class="submit-btn" id="submitWaterLevel">Submit</button>
        <button class="cancel-btn" id="closeWaterLevelModalFooter">Cancel</button>
      </div>
    </div>
  </div>



  <script>
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const webcamVideo = document.getElementById('webcamVideo');
    const homepageBtn = document.getElementById("homepageBtn");

    const bottleStatus = document.getElementById("bottleStatus");
    const capStatus = document.getElementById("capStatus");
    const waterLevelStatus = document.getElementById("waterLevelStatus");

    let ws = null;

    const waterLevelBtn = document.getElementById("waterlevelBtn");
    const waterLevelModal = document.getElementById("waterLevelModal");
    const closeWaterLevelModal = document.getElementById("closeWaterLevelModal");
    const closeWaterLevelModalFooter = document.getElementById("closeWaterLevelModalFooter");
    const submitWaterLevelBtn = document.getElementById("submitWaterLevel");

    // Open Modal
    waterLevelBtn.addEventListener("click", () => {
      waterLevelModal.style.display = "block";
    });

    // Close Modal when clicking close buttons
    closeWaterLevelModal.addEventListener("click", () => {
      waterLevelModal.style.display = "none";
    });

    closeWaterLevelModalFooter.addEventListener("click", () => {
      waterLevelModal.style.display = "none";
    });

    // Close modal when clicking outside the modal
    window.addEventListener("click", (event) => {
      if (event.target === waterLevelModal) {
        waterLevelModal.style.display = "none";
      }
    });

    // Handle Submit
    submitWaterLevelBtn.addEventListener("click", async () => {
      let percentage = parseFloat(document.getElementById("recommendedWaterLevel").value);

      // Validate input: Ensure it's a number and within 0-100 range
      if (isNaN(percentage) || percentage < 0 || percentage > 100) {
          alert("Please enter a valid water level between 0 and 100.");
          return;
      }

      let decimalValue = percentage / 100; // Convert percentage to decimal

      try {
          const response = await fetch("http://localhost:8000/quality_control/set-waterlevel", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ level: decimalValue }),
          });
    

          const result = await response.json();

          if (response.ok) {
              alert("Water level set successfully!");
              document.getElementById("recommendedWaterLevel").value = ""; // Clear input field
              waterLevelModal.style.display = "none"; // Close modal
          } else {
              alert(`Error: ${result.detail || "Failed to set water level."}`);
          }
      } catch (error) {
          console.error("Error:", error);
          alert("Something went wrong. Please try again.");
      }
    });

    homepageBtn.addEventListener("click", () => {
      window.location.href = "/";
    });

    startStreamBtn.addEventListener("click", () => {
      if (!ws) {
        ws = new WebSocket("ws://localhost:8000/quality_control/ws");

        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);

          if (data.error) {
              alert(data.error);  // Show alert if no camera is found
              webcamVideo.src = "";  // Clear video feed
              bottleStatus.textContent = "N/A";
              capStatus.textContent = "N/A";
              waterLevelStatus.textContent = "N/A";
              ws.close();
              return;
          }

          webcamVideo.src = "data:image/jpeg;base64," + data.image;

          if (data.bottle) bottleStatus.textContent = data.bottle;
          if (data.cap) capStatus.textContent = data.cap;
          if (data.waterLevel) waterLevelStatus.textContent = data.waterLevel;
        };

        ws.onclose = () => {
          console.log("WebSocket closed");
          ws = null;
        };

        ws.onerror = (error) => {
          console.error("WebSocket error:", error);
        };
      }
    });

    stopStreamBtn.addEventListener("click", () => {
      if (ws) {
        ws.send(JSON.stringify({ action: "stop" }));  // Send stop signal
        ws.close();
      }
      webcamVideo.src = "";
      bottleStatus.textContent = "N/A";
      capStatus.textContent = "N/A";
      waterLevelStatus.textContent = "N/A";
    });

  </script>

</body>
</html>
