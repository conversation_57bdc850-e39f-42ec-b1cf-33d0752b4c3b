#!/usr/bin/env python3
"""
Startup script for Crowd Detection Module
Run this to start the crowd detection module on port 8002
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Crowd Detection Module on http://localhost:8002")
    print("This module handles crowd detection and monitoring")
    print("Access module at: http://localhost:8002")
    print("Login at: http://localhost:8002/login")
    print("=" * 50)
    
    uvicorn.run(
        "crowd_detection.app.main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
