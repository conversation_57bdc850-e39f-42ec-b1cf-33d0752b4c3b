<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - Crowd Detection Dashboard</title>
  <link rel="stylesheet" href="{{ url_for('crowd_detection_static', path='crowd_detection.css') }}">
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }

    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      /* Make the sidebar fixed */
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto; /* In case the content is too long */
    }

    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
    }

    .logo svg {
      margin-right: 12px;
    }

    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
    }

    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }

    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }

    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }

    .system-status {
      margin-top: auto;
      padding: 15px 25px;
      font-size: 13px;
      background-color: rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #4caf50;
    }

    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      /* Add margin-left to prevent overlap with fixed sidebar */
      margin-left: 280px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }

    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }

    .page-title svg {
      margin-right: 12px;
      color: var(--primary);
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }

    .btn svg {
      margin-right: 8px;
    }

    .btn-primary {
      background-color: var(--primary);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-dark);
    }

    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }

    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--dark);
    }

    .camera-feeds {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
    }

    .camera-card {
      background-color: var(--card-bg);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: transform 0.2s ease;
      height: 300px; /* Fixed height for consistent layout */
      display: flex;
      flex-direction: column;
    }

    .camera-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .camera-feed {
      width: 100%;
      flex: 1; /* Take up all available space */
      background-color: #0d1b42;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden; /* Prevent image overflow */
    }

    .camera-feed img {
      width: 100%;
      height: 100%;
      object-fit: cover; /* Cover the container to fill all space */
      display: block; /* Remove any extra space */
      position: absolute; /* Position absolutely within the container */
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1; /* Ensure the image is above the background but below overlays */
    }

    /* Make sure all elements in the camera feed are clickable */
    .camera-feed * {
      pointer-events: none; /* Make all child elements pass through clicks to the parent */
    }

    /* Exception for the alert button which should receive clicks */
    .alert-btn-overlay {
      pointer-events: auto;
    }

    .status-badge {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 5px 12px;
      border-radius: 50px;
      font-size: 12px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
    }

    .live-badge {
      color: white;
    }

    .live-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #4caf50;
      margin-right: 6px;
    }

    .offline-badge {
      color: white;
    }

    .offline-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f44336;
      margin-right: 6px;
    }

    .camera-info-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 15px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      color: white;
      z-index: 10;
    }

    .camera-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: white;
    }

    .camera-meta-overlay {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .camera-last-detection {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.8);
    }

    .detection-count {
      background-color: rgba(46, 125, 50, 0.6);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
    }

    .alert-btn-overlay {
      position: absolute;
      top: 15px;
      left: 15px;
      background-color: rgba(46, 125, 50, 0.6);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      align-items: center;
      cursor: pointer;
      border: none;
      z-index: 20; /* Higher z-index to ensure it's clickable */
    }

    .alert-btn-overlay:hover {
      background-color: rgba(27, 94, 32, 0.8);
    }

    .alert-btn-overlay svg {
      margin-right: 6px;
      width: 16px;
      height: 16px;
    }

    /* Responsive adjustments */
    @media (max-width: 1200px) {
      .camera-feeds {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }

      .sidebar {
        width: 100%;
        position: relative;
        height: auto;
      }

      .content {
        margin-left: 0;
      }

      .camera-feeds {
        grid-template-columns: 1fr;
      }
    }

    /* Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      width: 80%;
      max-width: 600px;
      position: relative;
    }

    .close-btn {
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 24px;
      font-weight: bold;
      cursor: pointer;
      color: #757575;
    }

    .close-btn:hover {
      color: #000;
    }

    /* Form Styles */
    form label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    form input[type="text"],
    form input[type="email"],
    form input[type="number"],
    form input[type="time"] {
      width: 100%;
      padding: 10px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    form button {
      background-color: var(--primary);
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      margin-right: 10px;
    }

    form button:hover {
      background-color: var(--primary-dark);
    }

    #cameraList {
      margin-top: 20px;
      border-top: 1px solid #eee;
      padding-top: 15px;
    }

    #cameraList p {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;
    }

    #cameraList button {
      background-color: #f44336;
      padding: 5px 10px;
      font-size: 12px;
    }

    /* Alert Management Styles */
    .alert-buttons {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .schedule-container {
      margin-bottom: 20px;
    }

    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .checkbox-group label {
      display: flex;
      align-items: center;
      margin-bottom: 0;
    }

    .checkbox-group input[type="checkbox"] {
      margin-right: 5px;
    }

    .time-settings {
      display: flex;
      gap: 20px;
      margin-top: 15px;
    }

    .time-input {
      flex: 1;
    }

    .notification-settings {
      margin-bottom: 20px;
    }

    .input-group {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
    }

    .input-group input {
      flex: 1;
      margin-bottom: 0;
    }

    .recipient-list {
      margin-top: 10px;
    }

    .recipient-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 5px;
    }

    .remove-btn {
      background-color: #f44336;
      color: white;
      border: none;
      padding: 3px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    }

    .save-btn {
      background-color: var(--success);
      width: 100%;
      margin-top: 20px;
      padding: 12px;
    }

    .save-btn:hover {
      background-color: #1b5e20;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>
    <a href="/" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>
    <a href="/crowd_detection" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
      Crowd Detection
    </a>
    <a href="#" class="nav-item" id="setFocusAreaBtn">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 20h9"></path>
        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
      </svg>
      <span id="setFocusAreaBtnText">Set Focus Area</span>
    </a>
    <a href="#" class="nav-item" id="cameraManagementBtn">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
        <circle cx="12" cy="13" r="4"></circle>
      </svg>
      Camera Management
    </a>
    <a href="#" class="nav-item" id="alertManagementBtn">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
      </svg>
      Alert Management
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16 17 21 12 16 7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
      Logout
    </a>
  </div>

  <div class="content">
    <div class="header">
      <div class="page-title">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
        Crowd Detection Dashboard
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline" id="stopStreamBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Pause Monitoring
        </button>
        <button class="btn btn-primary" id="startStreamBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        </button>
      </div>
    </div>

    <div class="section-title">Live Camera Feeds</div>
    <div class="camera-feeds" id="feeds">
      <!-- Camera feeds will be added dynamically -->
    </div>
  </div>


 <!-- Popup window for managing cameras -->
 <div id="manageCameraModal" class="modal">
  <div class="modal-content">
    <span class="close-btn" id="closeModalBtn">&times;</span>
    <h2>Add RTSP Camera</h2>
    <form id="cameraForm">
      <label for="cameraName">Camera Name (Unique):</label>
      <input type="text" id="cameraName" name="cameraName" required><br><br>
      <label for="rtspUrl">RTSP URL:</label>
      <input type="text" id="rtspUrl" name="rtspUrl" required><br><br>
      <button type="submit">Add Camera</button>
      <button type="button" onclick="toggleList()">Show Cameras</button>
      <div style="display: none;" id="cameraList">
        <!-- Dynamically populate cameras and their delete buttons -->
      </div>
    </form>
  </div>
</div>



<!-- Popup window for managing alerts -->
<div id="manageAlertModal" class="modal">
  <div class="modal-content">
    <span class="close-btn" id="closeAlertModalBtn">&times;</span>
    <h2>Alert Management</h2>
    <div id="alertStatusContainer">
      <p>Alert Status: <span id="alertStatus" style="color: #f44336;">Inactive</span></p>
      <div class="alert-buttons">
        <button type="button" id="startAlertBtn">Start Alerts</button>
        <button type="button" id="stopAlertBtn">Stop Alerts</button>
      </div>
    </div>
    <form id="alertForm">
      <h3>Alert Schedule</h3>
      <div class="schedule-container">
        <div class="weekdays">
          <p>Active Days:</p>
          <div class="checkbox-group">
            <label><input type="checkbox" name="weekday" value="Monday"> Monday</label>
            <label><input type="checkbox" name="weekday" value="Tuesday"> Tuesday</label>
            <label><input type="checkbox" name="weekday" value="Wednesday"> Wednesday</label>
            <label><input type="checkbox" name="weekday" value="Thursday"> Thursday</label>
            <label><input type="checkbox" name="weekday" value="Friday"> Friday</label>
            <label><input type="checkbox" name="weekday" value="Saturday"> Saturday</label>
            <label><input type="checkbox" name="weekday" value="Sunday"> Sunday</label>
          </div>
        </div>
        <div class="time-settings">
          <div class="time-input">
            <label for="startTime">Start Time:</label>
            <input type="time" id="startTime" name="startTime">
          </div>
          <div class="time-input">
            <label for="endTime">End Time:</label>
            <input type="time" id="endTime" name="endTime">
          </div>
        </div>
      </div>

      <h3>Email Settings</h3>
      <div class="notification-settings">
        <div class="email-settings" id="emailSettings">
          <div class="input-group">
            <input type="email" id="newEmail" placeholder="Enter email address">
            <button type="button" id="addEmailBtn">Add</button>
          </div>
          <div id="emailList" class="recipient-list"></div>
        </div>
      </div>

      <h3>Alert Frequency</h3>
      <div class="frequency-settings">
        <label for="alertFrequency">Minimum time between alerts (minutes):</label>
        <input type="number" id="alertFrequency" name="alertFrequency" min="1" value="5">
      </div>

      <button type="submit" class="save-btn">Save Settings</button>
    </form>
  </div>
</div>





  <script>
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const feedsDiv = document.getElementById("feeds");

    let websocket = null;
    // Use localStorage to persist monitoring state and camera data across page navigation
    let cameraData = JSON.parse(localStorage.getItem('crowdCameraData') || '{}'); // Store camera data for reference
    let isMonitoring = localStorage.getItem('crowdMonitoringActive') === 'true'; // Flag to track if monitoring is active

    console.log("Initial monitoring state:", isMonitoring);
    console.log("Initial camera data:", cameraData);



    // Function to start the video stream
    async function startStream() {
      try {
        const response = await fetch('/crowd_detection/start-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream started successfully");
          startWebSocketFeed();
        } else {
          console.error("Failed to start stream");
        }
      } catch (error) {
        console.error("Error starting stream:", error);
      }
    }

    // Function to stop the video stream
    async function stopStream() {
      try {
        if (websocket) {
          websocket.onmessage = null; // Remove old message handler
          // websocket.close(); // Close the WebSocket connection
          // websocket = null; // Clear the WebSocket reference
        }
        const response = await fetch('/crowd_detection/stop-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream stopped successfully");
          feedsDiv.innerHTML = ""; // Clear video feeds
          cameraData = {}; // Clear camera data
        } else {
          console.error("Failed to stop stream");
        }
      } catch (error) {
        console.error("Error stopping stream:", error);
      }
    }

    // Function to start the WebSocket connection and handle incoming frames and counts
  //  function startWebSocketFeed() {
  //     // if (websocket) {
  //     //   websocket.close(); // Close existing WebSocket connection if any
  //     // }


  //     websocket = new WebSocket("ws://localhost:8000/crowd_detection/ws");

  //     websocket.onmessage = (event) => {
  //       const message = event.data;
  //       const [cameraId, cam_name, data, count, roiCountsStr] = message.split(':');

  //       let roiCounts = []
  //       try {
  //         roiCounts = JSON.parse(roiCountsStr); // Safely parse ROI counts if JSON is valid
  //           } catch (error) {
  //         console.warn("Failed to parse ROI counts:", error);
  //         }


  //     // Update video feed
  //     let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
  //     if (!cameraContainer) {
  //       // Create a new container for this camera
  //       cameraContainer = document.createElement("div");
  //       cameraContainer.id = `camera-container-${cameraId}`;
  //       cameraContainer.classList.add("camera-container");

  //       // Add camera label
  //       const cameraLabel = document.createElement("h3");
  //       // cameraLabel.textContent = `Camera ${parseInt(cameraId) + 1}`;
  //       cameraLabel.textContent = cam_name;
  //       cameraContainer.appendChild(cameraLabel);

  //       // Add the video feed
  //       const imgElem = document.createElement("img");
  //       imgElem.id = `feed-${cameraId}`;
  //       imgElem.alt = "Processed Video Feed";
  //       cameraContainer.appendChild(imgElem);

  //       // Append the camera container to the feeds
  //       feedsDiv.appendChild(cameraContainer);
  //     }

  //     // Update the video feed image
  //     const imgElem = document.querySelector(`#feed-${cameraId}`);
  //     imgElem.src = `data:image/jpeg;base64,${data}`;

  //     // Update live observations
  //     let observationElem = document.querySelector(`#observation-${cameraId}`);
  //     if (!observationElem) {
  //       // Create a new list item if it doesn't exist
  //       observationElem = document.createElement("li");
  //       observationElem.id = `observation-${cameraId}`;
  //       liveObservations.appendChild(observationElem);
  //     }

  //     // Format observations
  //     // let observationText = `
  //     //   Camera ${parseInt(cameraId) + 1}<br>
  //     //     Total People Count: ${count}<br>
  //     // `;


  //     let observationText = `
  //        ${cam_name}<br><br>
  //         Total People Count: ${count}<br>
  //     `;

  //     // Add ROI details if they exist
  // if (roiCounts && roiCounts.length > 0) {
  //   roiCounts.forEach((roiCount, idx) => {
  //     observationText += `  Region ${idx + 1}: ${roiCount}<br>`;
  //     });
  //   } else {
  //   observationText += `No ROI data available.<br>`;
  //   }

  //   // Set the HTML content to include line breaks
  //   observationElem.innerHTML = observationText.trim();
  //   };

  //   websocket.onerror = (error) => {
  //     console.error("WebSocket error:", error);
  //   };

  //   websocket.onclose = () => {
  //     console.log("WebSocket closed");
  //   };
  // }


  // Function to expand a camera - only works when monitoring is active
  function expandCamera(cameraId, cameraName) {
    console.log("Expanding camera:", cameraId, cameraName);
    // Store the selected camera info in sessionStorage
    sessionStorage.setItem('selectedCamera', JSON.stringify({
        id: cameraId,
        name: cameraName,
        data: cameraData[cameraId] || {}
    }));

    // Redirect to the expanded view with camera ID and name
    window.location.href = `/crowd_detection/expand?id=${cameraId}&name=${encodeURIComponent(cameraName)}`;
  }

  function startWebSocketFeed() {
    // Initialize WebSocket connection
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = window.location.host;
    const wsUrl = `${wsProtocol}//${wsHost}/crowd_detection/ws`;

    console.log("Connecting to WebSocket at:", wsUrl);

    websocket = new WebSocket(wsUrl);
    websocket.binaryType = 'arraybuffer'; // Set WebSocket to receive binary data

    let lastMetadata = null; // Store metadata for the next binary frame

    websocket.onopen = () => {
        console.log("WebSocket connection established.");
    };

    websocket.onmessage = (event) => {
        console.log("WebSocket message received:", typeof event.data);

        if (typeof event.data === 'string') {
            // Handle metadata (text message)
            console.log("Metadata received:", event.data);
            lastMetadata = event.data.split(':');
        } else if (event.data instanceof ArrayBuffer) {
            // Handle binary frame (image data)
            console.log("Binary data received, length:", event.data.byteLength);

            if (lastMetadata) {
                const [cameraId, cam_name, count, roiCountsStr] = lastMetadata;
                console.log("Processing camera:", cameraId, cam_name, "Count:", count);

                let roiCounts = [];
                try {
                    roiCounts = JSON.parse(roiCountsStr); // Parse ROI counts
                } catch (error) {
                    console.warn("Failed to parse ROI counts:", error);
                }

                try {
                    // Store camera data for reference
                    cameraData[cameraId] = {
                        name: cam_name,
                        count: count,
                        roiCounts: roiCounts,
                        lastUpdate: new Date()
                    };

                    // Store camera data in localStorage for persistence across page navigation
                    localStorage.setItem('crowdCameraData', JSON.stringify(cameraData));

                    // Find the existing camera card (should already exist from loadCamerasAndCreatePlaceholders)
                    let cameraCard = document.getElementById(`camera-${cameraId}`);
                    if (cameraCard) {
                        console.log("Found camera card for:", cameraId);

                        // Update the camera feed image
                        const cameraFeedDiv = cameraCard.querySelector('.camera-feed');
                        if (cameraFeedDiv) {
                            console.log("Updating camera feed div");

                            // Convert ArrayBuffer to base64
                            const binary = [];
                            const bytes = new Uint8Array(event.data);
                            const len = bytes.byteLength;
                            for (let i = 0; i < len; i++) {
                                binary.push(String.fromCharCode(bytes[i]));
                            }
                            const base64Image = window.btoa(binary.join(''));
                            const dataUrl = `data:image/jpeg;base64,${base64Image}`;

                            // Save the camera info overlay and alert button
                            const cameraInfoOverlay = cameraFeedDiv.querySelector('.camera-info-overlay');
                            const alertButton = cameraFeedDiv.querySelector('.alert-btn-overlay');

                            // Clear the camera feed div
                            cameraFeedDiv.innerHTML = '';

                            // Create a new image element
                            const img = new Image();
                            img.id = `camera-img-${cameraId}`;
                            img.alt = `Camera Feed ${cameraId}`;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'cover'; // Use cover to fill the space
                            img.style.position = 'absolute';
                            img.style.top = '0';
                            img.style.left = '0';
                            img.style.right = '0';
                            img.style.bottom = '0';
                            img.style.zIndex = '1';

                            // Add error handling for the image
                            img.onerror = function() {
                                console.error("Failed to load image for camera:", cameraId);
                                this.onerror = null; // Prevent infinite error loop
                                this.src = ''; // Clear the source

                                // Show error placeholder
                                cameraFeedDiv.innerHTML = `
                                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #0d1b42;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ff5252" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <p style="color: white; margin-top: 10px;">Error loading camera feed</p>
                                    </div>
                                    <div class="status-badge live-badge">LIVE</div>
                                `;

                                // Re-add the camera info overlay and alert button if they existed
                                if (cameraInfoOverlay) {
                                    cameraFeedDiv.appendChild(cameraInfoOverlay);
                                }

                                if (alertButton) {
                                    cameraFeedDiv.appendChild(alertButton);
                                }
                            };

                            // Set the image source after defining the error handler
                            img.src = dataUrl;

                            // Add the image to the DOM
                            cameraFeedDiv.appendChild(img);

                            // Add the status badge
                            const statusBadge = document.createElement('div');
                            statusBadge.className = 'status-badge live-badge';
                            statusBadge.textContent = 'LIVE';
                            cameraFeedDiv.appendChild(statusBadge);

                            // Re-add the camera info overlay and alert button
                            if (cameraInfoOverlay) {
                                cameraFeedDiv.appendChild(cameraInfoOverlay);
                            } else {
                                // Create new camera info overlay if it doesn't exist
                                const newInfoOverlay = document.createElement('div');
                                newInfoOverlay.className = 'camera-info-overlay';
                                newInfoOverlay.innerHTML = `
                                    <div class="camera-title">${cam_name}</div>
                                    <div class="camera-meta-overlay">
                                        <div class="camera-last-detection" id="last-detection-${cameraId}">Last detection: just now</div>
                                        <div class="detection-count" id="detection-count-${cameraId}">${count} people detected</div>
                                    </div>
                                `;
                                cameraFeedDiv.appendChild(newInfoOverlay);
                            }

                            if (alertButton) {
                                cameraFeedDiv.appendChild(alertButton);
                            } else {
                                // Create new alert button if it doesn't exist
                                const newAlertButton = document.createElement('button');
                                newAlertButton.className = 'alert-btn-overlay';
                                newAlertButton.innerHTML = `
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                                        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                                    </svg>
                                    Alert
                                `;
                                // Add click event listener to the alert button
                                newAlertButton.addEventListener('click', function(event) {
                                    event.stopPropagation(); // Prevent the card click event from firing
                                    alert('Alert configuration will be implemented in a future update.');
                                });
                                cameraFeedDiv.appendChild(newAlertButton);
                            }
                        } else {
                            console.warn("Camera feed div not found for camera:", cameraId);
                        }

                        // Update detection count
                        const detectionCount = document.getElementById(`detection-count-${cameraId}`);
                        if (detectionCount) {
                            detectionCount.textContent = `${count} people detected`;
                        }

                        // Update last detection time
                        const lastDetection = document.getElementById(`last-detection-${cameraId}`);
                        if (lastDetection) {
                            lastDetection.textContent = "Last detection: just now";
                        }
                    } else {
                        console.warn("Camera card not found for camera:", cameraId);
                    }
                } catch (error) {
                    console.error("Error processing camera frame:", error);
                }

                // Reset metadata after processing
                lastMetadata = null;
            } else {
                console.warn("Received binary data without metadata");
            }
        }
    };

    websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
        // Don't show alert as it might be annoying with frequent errors
        console.log("WebSocket connection error. Will try to reconnect automatically.");
    };

    websocket.onclose = (event) => {
        console.log("WebSocket connection closed. Code:", event.code, "Reason:", event.reason);

        if (isMonitoring) {
            // Try to reconnect if the connection was closed unexpectedly
            console.log("Attempting to reconnect WebSocket in 2 seconds...");
            setTimeout(() => {
                if (isMonitoring) {
                    console.log("Reconnecting WebSocket...");
                    startWebSocketFeed();
                }
            }, 2000);
        }
    };
}

    // Function to load cameras from the server and create placeholder cards
    async function loadCamerasAndCreatePlaceholders() {
      try {
        const response = await fetch("/crowd_detection/get-cameras", {
          method: "GET",
        });
        const cameras = await response.json();

        // Clear existing camera cards
        feedsDiv.innerHTML = "";

        // Create a placeholder card for each camera
        let cameraIndex = 0;
        for (const cameraName in cameras) {
          if (cameras.hasOwnProperty(cameraName)) {
            const cameraId = cameraIndex.toString();

            // Create placeholder camera card
            const card = document.createElement("div");
            card.id = `camera-${cameraId}`;
            card.className = "camera-card";
            card.dataset.cameraId = cameraId;
            card.dataset.cameraName = cameraName;

            card.innerHTML = `
              <div class="camera-feed">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background-color: #0d1b42;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                    <circle cx="12" cy="13" r="4"></circle>
                  </svg>
                </div>
                <div class="status-badge offline-badge">OFFLINE</div>
                <button class="alert-btn-overlay">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                  </svg>
                  Alert
                </button>
                <div class="camera-info-overlay">
                  <div class="camera-title">${cameraName}</div>
                  <div class="camera-meta-overlay">
                    <div class="camera-last-detection" id="last-detection-${cameraId}">Camera not active</div>
                    <div class="detection-count" id="detection-count-${cameraId}">0 people detected</div>
                  </div>
                </div>
              </div>
            `;

            feedsDiv.appendChild(card);

            // Add click event to expand the camera (only when monitoring is active)
            card.addEventListener("click", function() {
              if (isMonitoring) {
                expandCamera(cameraId, cameraName);
              } else {
                alert("Please start monitoring first to view camera details.");
              }
            });

            // Add event listener to the alert button
            const alertBtn = card.querySelector('.alert-btn-overlay');
            if (alertBtn) {
              alertBtn.addEventListener('click', function(event) {
                event.stopPropagation(); // Prevent the card click event from firing
                alert('Alert configuration will be implemented in a future update.');
              });
            }

            cameraIndex++;
          }
        }

        // If no cameras found, show a message
        if (cameraIndex === 0) {
          feedsDiv.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 50px; background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                <circle cx="12" cy="13" r="4"></circle>
              </svg>
              <h3 style="margin-top: 20px; color: #333;">No cameras found</h3>
              <p style="margin-top: 10px; color: #666;">Please add cameras using the Camera Management option.</p>
            </div>
          `;
        }

      } catch (error) {
        console.error("Error loading cameras:", error);
        feedsDiv.innerHTML = `
          <div style="grid-column: 1 / -1; text-align: center; padding: 50px; background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
            <h3 style="color: #d32f2f;">Error loading cameras</h3>
            <p style="margin-top: 10px; color: #666;">Please check your connection and try again.</p>
          </div>
        `;
      }
    }

    // Function to update camera cards when monitoring starts
    function updateCameraStatusToLive() {
      const cameraCards = document.querySelectorAll('.camera-card');
      cameraCards.forEach(card => {
        const statusBadge = card.querySelector('.status-badge');
        if (statusBadge) {
          statusBadge.className = 'status-badge live-badge';
          statusBadge.textContent = 'LIVE';
        }
      });
    }

    // Function to update camera cards when monitoring stops
    function updateCameraStatusToOffline() {
      const cameraCards = document.querySelectorAll('.camera-card');
      cameraCards.forEach(card => {
        const statusBadge = card.querySelector('.status-badge');
        if (statusBadge) {
          statusBadge.className = 'status-badge offline-badge';
          statusBadge.textContent = 'OFFLINE';
        }

        // Reset detection count and last detection
        const cameraId = card.dataset.cameraId;
        const detectionCount = document.getElementById(`detection-count-${cameraId}`);
        const lastDetection = document.getElementById(`last-detection-${cameraId}`);

        if (detectionCount) detectionCount.textContent = '0 people detected';
        if (lastDetection) lastDetection.textContent = 'Camera not active';

        // Reset the camera feed image to the offline placeholder
        const cameraFeedDiv = card.querySelector('.camera-feed');
        if (cameraFeedDiv) {
          // Save the camera info overlay and alert button
          const cameraInfoOverlay = cameraFeedDiv.querySelector('.camera-info-overlay');
          const alertButton = cameraFeedDiv.querySelector('.alert-btn-overlay');

          // Clear the camera feed div
          cameraFeedDiv.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background-color: #0d1b42;">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                <circle cx="12" cy="13" r="4"></circle>
              </svg>
            </div>
            <div class="status-badge offline-badge">OFFLINE</div>
          `;

          // Re-add the camera info overlay and alert button if they existed
          if (cameraInfoOverlay) {
            cameraFeedDiv.appendChild(cameraInfoOverlay);
          }

          if (alertButton) {
            cameraFeedDiv.appendChild(alertButton);
          }
        }
      });
    }

    // Modified startStream function to set monitoring flag
    async function startStreamModified() {
      if (isMonitoring) return; // Already monitoring

      try {
        const response = await fetch('/crowd_detection/start-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream started successfully");
          isMonitoring = true;
          // Store monitoring state in localStorage
          localStorage.setItem('crowdMonitoringActive', 'true');
          updateCameraStatusToLive();
          startWebSocketFeed();
        } else {
          console.error("Failed to start stream");
        }
      } catch (error) {
        console.error("Error starting stream:", error);
      }
    }

    // Modified stopStream function to reset monitoring flag
    async function stopStreamModified() {
      if (!isMonitoring) return; // Not monitoring

      try {
        if (websocket) {
          websocket.onmessage = null; // Remove old message handler
          // websocket.close(); // Close the WebSocket connection
          // websocket = null; // Clear the WebSocket reference
        }
        const response = await fetch('/crowd_detection/stop-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream stopped successfully");
          isMonitoring = false;
          // Store monitoring state in localStorage
          localStorage.setItem('crowdMonitoringActive', 'false');
          updateCameraStatusToOffline();
          cameraData = {}; // Clear camera data
          // Clear camera data in localStorage
          localStorage.setItem('crowdCameraData', '{}');
        } else {
          console.error("Failed to stop stream");
        }
      } catch (error) {
        console.error("Error stopping stream:", error);
      }
    }

    // Load cameras and initialize monitoring when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Load camera placeholders
      loadCamerasAndCreatePlaceholders();

      // If monitoring was active, update UI and reconnect
      if (isMonitoring) {
        console.log("Monitoring was active, reconnecting...");
        updateCameraStatusToLive();
        startWebSocketFeed();
      }
    });

    // Event listeners for start and stop buttons
    startStreamBtn.addEventListener('click', startStreamModified);
    stopStreamBtn.addEventListener('click', stopStreamModified);









    // MANAGE CAMERA BUTTON POPUP
    let showList = false
    const cameraManagementBtn = document.getElementById("cameraManagementBtn");
    const modal = document.getElementById("manageCameraModal");
    const closeModalBtn = document.getElementById("closeModalBtn");
    const cameraForm = document.getElementById("cameraForm");

    function toggleList(){
      showList = !showList
      const cameraList = document.getElementById('cameraList')
      if(showList){
        cameraList.style.display = 'block'
      }else{
        cameraList.style.display = 'none'
      }
    }
    // Show modal when 'Camera Management' button is clicked
    cameraManagementBtn.addEventListener("click", () => {
      modal.style.display = "block";
      loadCameras(); // Load the camera list when opening the modal
    });

    // Close modal when the close button is clicked
    closeModalBtn.addEventListener("click", () => {
      modal.style.display = "none";
    });

    // Close modal when clicking outside the modal
    window.addEventListener("click", (event) => {
      if (event.target === modal) {
        modal.style.display = "none";
      }
    });

    // Handle form submission to add the camera
    cameraForm.addEventListener("submit", async (e) => {
    e.preventDefault();

    const cameraName = document.getElementById("cameraName").value;
    const rtspUrl = document.getElementById("rtspUrl").value;

    // Send data to backend to save the camera information
    const response = await fetch('/crowd_detection/add-camera', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ cameraName, rtspUrl }),
    });

    const result = await response.json();

    if (result.status === 'success') {
      alert('Camera added successfully!');
      cameraForm.reset()
      loadCameras()
    } else if (result.status === 'error') {
      // Show the existing camera name in the alert message
      alert(`Error: RTSP URL already exists for camera: ${result.message.split(': ')[1]}`);
    }
    else if (result.status === 'samename') {
      alert(`Same Name Already Exist`);
    }else {
      alert('Error adding camera.');
    }
  });

  // Function to load and display the list of cameras
  async function loadCameras() {
    const response = await fetch("/crowd_detection/get-cameras", {
      method: "GET",
    });
    const cameras = await response.json();

    const cameraList = document.getElementById("cameraList");
    cameraList.innerHTML = ""; // Clear previous list

    for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
      const cameraItem = document.createElement("div");
      cameraItem.innerHTML = `
        <p>
          <strong>${cameraName}</strong>:
          <button onclick="deleteCamera('${cameraName}')">Delete</button>
        </p>
      `;
      cameraList.appendChild(cameraItem);
    }
  }

  // Function to delete a camera
  async function deleteCamera(cameraName) {
    const confirmed = confirm(`Are you sure you want to delete the camera "${cameraName}"?`);
    if (!confirmed) return;

    const response = await fetch(`/crowd_detection/delete-camera/${cameraName}`, {
      method: "DELETE",
    });

    const result = await response.json();

    if (result.status === "success") {
      alert(result.message);
      loadCameras(); // Refresh the list
    } else {
      alert("Error deleting camera: " + result.message);
    }
  }

  // Load cameras when the page loads
  document.addEventListener('DOMContentLoaded', () => {
    loadCamerasAndCreatePlaceholders();
  });



// Alert Management Modal Elements
const alertManagementBtn = document.getElementById("alertManagementBtn");
const alertModal = document.getElementById("manageAlertModal");
const closeAlertModalBtn = document.getElementById("closeAlertModalBtn");
const alertForm = document.getElementById("alertForm");
const startAlertBtn = document.getElementById("startAlertBtn");
const stopAlertBtn = document.getElementById("stopAlertBtn");
const alertStatus = document.getElementById("alertStatus");
const addEmailBtn = document.getElementById("addEmailBtn");
const emailList = document.getElementById("emailList");

// Variable to track alert status
let isAlertActive = false;
let emailRecipients = [];

// Show modal when 'Alert Management' button is clicked
alertManagementBtn.addEventListener("click", () => {
  alertModal.style.display = "block";
  loadAlertSettings(); // Load existing settings
});

// Close modal when the close button is clicked
closeAlertModalBtn.addEventListener("click", () => {
  alertModal.style.display = "none";
});

// Close modal when clicking outside the modal
window.addEventListener("click", (event) => {
  if (event.target === alertModal) {
    alertModal.style.display = "none";
  }
});

// Start Alert Button Click Handler
startAlertBtn.addEventListener("click", async () => {
  try {
    const response = await fetch('/crowd_detection/start-alerts', {
      method: 'POST',
    });

    if (response.ok) {
      isAlertActive = true;
      alertStatus.textContent = "Active";
      alertStatus.style.color = "#4CAF50";
      console.log("Alerts started successfully");
    } else {
      console.error("Failed to start alerts");
    }
  } catch (error) {
    console.error("Error starting alerts:", error);
  }
});

// Stop Alert Button Click Handler
stopAlertBtn.addEventListener("click", async () => {
  try {
    const response = await fetch('/crowd_detection/stop-alerts', {
      method: 'POST',
    });

    if (response.ok) {
      isAlertActive = false;
      alertStatus.textContent = "Inactive";
      alertStatus.style.color = "#f44336";
      console.log("Alerts stopped successfully");
    } else {
      console.error("Failed to stop alerts");
    }
  } catch (error) {
    console.error("Error stopping alerts:", error);
  }
});

// Add email recipient
addEmailBtn.addEventListener("click", function() {
  const emailInput = document.getElementById("newEmail");
  const email = emailInput.value.trim();

  if (validateEmail(email)) {
    if (!emailRecipients.includes(email)) {
      emailRecipients.push(email);
      renderEmailList();
      emailInput.value = "";
    } else {
      alert("This email address is already added.");
    }
  } else {
    alert("Please enter a valid email address.");
  }
});

// Render email list
function renderEmailList() {
  emailList.innerHTML = "";
  emailRecipients.forEach((email, index) => {
    const item = document.createElement("div");
    item.className = "recipient-item";
    item.innerHTML = `
      <span>${email}</span>
      <button type="button" class="remove-btn" data-index="${index}">Remove</button>
    `;
    emailList.appendChild(item);
  });

  // Add event listeners to remove buttons
  addRemoveListeners();
}

// Add listeners to remove buttons
function addRemoveListeners() {
  document.querySelectorAll(".remove-btn").forEach(button => {
    button.addEventListener("click", function() {
      const index = parseInt(this.getAttribute("data-index"));
      emailRecipients.splice(index, 1);
      renderEmailList();
    });
  });
}

// Validate email format
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

// Handle form submission to save alert settings
alertForm.addEventListener("submit", async (e) => {
  e.preventDefault();

  // Collect selected days
  const selectedDays = [];
  document.querySelectorAll('input[name="weekday"]:checked').forEach(checkbox => {
    selectedDays.push(checkbox.value);
  });

  const startTime = document.getElementById("startTime").value;
  const endTime = document.getElementById("endTime").value;
  const alertFrequency = document.getElementById("alertFrequency").value;

  // Create settings object
  const settings = {
    active: isAlertActive,
    days: selectedDays,
    startTime: startTime,
    endTime: endTime,
    alertFrequency: parseInt(alertFrequency),
    recipients: emailRecipients
  };

  try {
    // Send settings to backend
    const response = await fetch('/crowd_detection/save-alert-settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),

    });

    const result = await response.json();

    if (result.status === 'success') {
      alert('Alert settings saved successfully!');
    } else {
      alert('Error saving alert settings: ' + (result.message || ''));
    }
  } catch (error) {
    console.error("Error saving alert settings:", error);
    alert('Error saving alert settings.');
  }
});

// Function to load existing alert settings
async function loadAlertSettings() {
  try {
    const response = await fetch("/crowd_detection/get-alert-settings", {
      method: "GET",
    });

    if (response.ok) {
      const settings = await response.json();

      // Update form with saved settings
      isAlertActive = settings.active;
      alertStatus.textContent = settings.active ? "Active" : "Inactive";
      alertStatus.style.color = settings.active ? "#4CAF50" : "#f44336";

      // Check the appropriate day checkboxes
      document.querySelectorAll('input[name="weekday"]').forEach(checkbox => {
        checkbox.checked = settings.days.includes(checkbox.value);
      });

      // Set time values
      document.getElementById("startTime").value = settings.startTime || "";
      document.getElementById("endTime").value = settings.endTime || "";
      document.getElementById("alertFrequency").value = settings.alertFrequency || 5;

      // Load email recipients
      emailRecipients = settings.recipients || [];
      renderEmailList();

    } else {
      console.error("Failed to load alert settings");
    }
  } catch (error) {
    console.error("Error loading alert settings:", error);
  }
}



  </script>
</body>
</html>