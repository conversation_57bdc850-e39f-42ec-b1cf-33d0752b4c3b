import smtplib
import ssl
import random
import string
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from sqlalchemy.orm import Session

from config import settings
from models import OTP

def generate_otp(length=6):
    """Generate a random OTP code of specified length"""
    return ''.join(random.choices(string.digits, k=length))

def create_otp(db: Session, email: str):
    """Create a new OTP record in the database"""
    # Invalidate any existing OTPs for this email
    existing_otps = db.query(OTP).filter(OTP.email == email, OTP.is_used == False).all()
    for otp in existing_otps:
        otp.is_used = True

    # Generate a new OTP
    code = generate_otp()

    # Create new OTP record
    db_otp = OTP(email=email, code=code)
    db.add(db_otp)
    db.commit()
    db.refresh(db_otp)

    return db_otp

def verify_otp(db: Session, email: str, code: str):
    """Verify if the OTP is valid for the given email"""
    print(f"Verifying OTP: email={email}, code={code}")

    # First, check if there are any OTPs for this email
    all_otps = db.query(OTP).filter(OTP.email == email).all()
    print(f"Found {len(all_otps)} OTPs for email {email}")
    for otp_record in all_otps:
        print(f"  OTP: {otp_record.code}, Used: {otp_record.is_used}, Valid: {otp_record.is_valid()}, Expires: {otp_record.expires_at}")

    # Now look for the specific OTP
    otp = db.query(OTP).filter(
        OTP.email == email,
        OTP.code == code,
        OTP.is_used == False
    ).first()

    if not otp:
        print(f"No matching OTP found for email={email}, code={code}")
        return False

    print(f"Found OTP: {otp.code}, checking validity")
    if not otp.is_valid():
        print(f"OTP is not valid. Expired at: {otp.expires_at}")
        return False

    # Mark OTP as used
    print(f"OTP is valid, marking as used")
    otp.is_used = True
    db.commit()

    return True

def send_otp_email(otp: OTP):
    """Send OTP verification email"""
    try:
        # Email settings from config
        smtp_server = settings.SMTP_SERVER
        port = settings.SMTP_PORT
        sender_email = settings.SMTP_USERNAME
        password = settings.SMTP_PASSWORD

        # Create message
        message = MIMEMultipart("alternative")
        message["Subject"] = "Crowd Detection Service Verification Code"
        message["From"] = sender_email

        # Email content
        html = f"""
        <html>
          <body>
            <h2>Crowd Detection Service Verification</h2>
            <p>A new user is trying to register with email: {otp.email}</p>
            <p><strong>Verification Code:</strong> {otp.code}</p>
            <p>This code will expire in {settings.OTP_EXPIRY_MINUTES} minutes (at {otp.expires_at.strftime('%Y-%m-%d %H:%M:%S')})</p>
            <p>If you did not request this code, please ignore this email.</p>
          </body>
        </html>
        """

        # Attach HTML content
        part = MIMEText(html, "html")
        message.attach(part)

        # Create a secure SSL context
        context = ssl.create_default_context()

        # Connect to server
        with smtplib.SMTP(smtp_server, port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)

            # Send to admin email
            recipient = settings.ADMIN_EMAIL
            message["To"] = recipient
            server.sendmail(sender_email, recipient, message.as_string())

        return True

    except Exception as e:
        print(f"Error sending email: {e}")
        return False
