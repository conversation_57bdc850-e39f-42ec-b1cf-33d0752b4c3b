from app.core.database import create_tables, create_auth_tables
from app.core.admin import setup_default_users

def setup_database():
    """
    Complete database setup script that:
    1. Creates the necessary tables for both databases
    2. Creates admin and regular users with appropriate roles
    
    This script is useful for setting up a new environment or
    recreating the database from scratch.
    """
    print("Starting database setup...")
    
    # Step 1: Create tables for main database
    print("\n--- Creating main database tables ---")
    create_tables()
    
    # Step 2: Create tables for auth database
    print("\n--- Creating auth database tables ---")
    create_auth_tables()
    
    # Step 3: Create default users (admin and regular)
    print("\n--- Creating default users ---")
    setup_default_users()
    
    print("\nDatabase setup complete!")

if __name__ == "__main__":
    setup_database()
