from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# Auth database URL (vigilanteye)
AUTH_SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.AUTH_DB_USER}:{settings.AUTH_DB_PASSWORD}@{settings.AUTH_DB_HOST}:{settings.AUTH_DB_PORT}/{settings.AUTH_DB_NAME}"
)

# Create engine for auth database
auth_engine = create_engine(AUTH_SQLALCHEMY_DATABASE_URL)
AuthSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=auth_engine)

# Create declarative base for auth database
AuthBase = declarative_base()

def get_auth_db():
    """Dependency to get auth database session"""
    db = AuthSessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_auth_tables():
    """Create auth database tables"""
    AuthBase.metadata.create_all(bind=auth_engine)
