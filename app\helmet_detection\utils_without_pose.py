import cv2
from ultralytics import YOLO
import numpy as np
import threading


class Helmetdetection:
    def __init__(self, model_path,conf_threshold):
        self.conf_threshold = conf_threshold
        self.model = YOLO(model_path)  
        self.threads = []
        self.running = False
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (640, 640),interpolation=cv2.INTER_AREA)
        self.class_labels = ['boots', 'gloves', 'helmet', 'person', 'safety-glass', 'vest']

    def start(self, camera_details, test_video_path=None):
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        # If test_video_path is provided, override camera details
        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0])
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.warning_count = [None] * len(self.cap_devices)
        self.helmet_frames = [None] * len(self.cap_devices)

        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(
                        self.blank,
                        f"{self.camera_name[idx]} is Offline",
                        (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".jpg", temp)
                    self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        for thread in self.threads:
            thread.join()

    def segment_objects(self, frame):
        results = self.model(frame,stream = True,classes = [3,4,5,6])  # Run inference
        detections = []
        person_masks = []
        ppe_masks = {'no-helmet': [], 'no-vest': [], 'no-gloves': []}
        
        for result in results:
            print(result.masks.data[0].cpu().numpy())
            for i in range(len(result.boxes)):
                box = result.boxes.xyxy[i].cpu().numpy().astype(int)
                # print("box",box)
                score = result.boxes.conf[i].item()
                # print("score",score)
                label_id = int(result.boxes.cls[i].item())
                # print(label_id)
                label = self.class_labels[label_id]
                # print(label)

                if score >= self.conf_threshold:
                    mask = result.masks.data[i].cpu().numpy() if result.masks is not None else None
                    detections.append({'label': label, 'box': box, 'mask': mask})
                    if mask is not None:
                        if label == 'person':
                            person_masks.append(mask)
                        elif label in ppe_masks:
                            ppe_masks[label].append(mask)
        
        return detections, person_masks, ppe_masks

    def check_ppe_compliance(self, person_masks, ppe_masks):
        """Check if PPE masks overlap with the person mask"""

        compliance_results = []
        
        # Check PPE for each person
        for person_mask in person_masks:
            person_status = {"no-helmet": False, "no-vest": False, "no-gloves": False}
            # Check mask overlap for each PPE
            for ppe, ppe_mask_list in ppe_masks.items():
                for ppe_mask in ppe_mask_list:
                    if self.mask_overlap(person_mask, ppe_mask):
                        person_status[ppe] = True

            compliance_results.append({"mask": person_mask, "status": person_status})

        return compliance_results

    def mask_overlap(self, person_mask, ppe_mask, threshold=.001):
        """Check if PPE mask significantly overlaps with the person mask"""
        overlap = np.logical_and(person_mask, ppe_mask).sum()
        # print(overlap)
        # person_area = person_mask.sum()
        object_area = ppe_mask.sum()
        # print("overlap",overlap)
        # print("object_area",object_area)
        # print("percentage",(overlap / object_area)*100)
        return (overlap / object_area) > threshold  # At least 1% overlap
    

    def process_frame(self, frame):
        detections, person_masks, ppe_masks = self.segment_objects(frame)
        compliance_results = self.check_ppe_compliance(person_masks, ppe_masks)
        
        # Draw bounding boxes and compliance status
        for result in compliance_results:
            person_mask = result["mask"]
            status = result["status"]
            missing_items = [ppe for ppe, worn in status.items() if not worn]

            color = (0, 255, 0) if not missing_items else (0, 0, 255)  # Green if compliant, Red if missing PPE
            
            # Get bounding box from mask
            y_indices, x_indices = np.where(person_mask > 0)
            if len(y_indices) > 0 and len(x_indices) > 0:
                x1, y1 = x_indices.min(), y_indices.min()
                x2, y2 = x_indices.max(), y_indices.max()

                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                # Display missing PPE items
                text = "OK" if not missing_items else "Missing: " + ", ".join(missing_items)
                cv2.putText(frame, text, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        return frame

    def update(self, idx, cap, test_video_path=None):
        frame_counter = 0
        skip_frames = 2
        while self.running:
            ret, frame = cap.read()
            
            # If video is finished, loop it for testing
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Restart the video
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            frame = cv2.resize(frame, (640, 640))

            processed_frame = self.process_frame(frame)

            cv2.imshow("frame", processed_frame)
            cv2.waitKey(1)


            _, buffer = cv2.imencode(".jpg", processed_frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            self.helmet_frames[idx] = buffer.tobytes()
            

            if not self.running:
                break

        cv2.destroyAllWindows()