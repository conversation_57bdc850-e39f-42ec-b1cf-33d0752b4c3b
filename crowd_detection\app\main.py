from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import crowd detection modules
from routes import router as crowd_detection_router
from database import create_tables

# Import middleware
from middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Crowd Detection Service", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# Note: Authentication is handled by separate auth service

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "templates")
if not os.path.exists(templates_dir):
    os.makedirs(templates_dir, exist_ok=True)

templates = Jinja2Templates(directory=templates_dir)
app.mount("/static", StaticFiles(directory="crowd_detection/static"), name="static")

# Include crowd detection routes
app.include_router(crowd_detection_router, prefix="", tags=["Crowd Detection"])

# Home page - direct access, no authentication required
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("crowd_detection.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
