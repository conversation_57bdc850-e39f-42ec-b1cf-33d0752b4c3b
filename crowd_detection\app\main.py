from fastapi import <PERSON><PERSON><PERSON>, Request, Form, Depends, status, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON><PERSON><PERSON>
from datetime import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import crowd detection modules
from routes import router as crowd_detection_router
from database import get_db, get_auth_db, create_tables, create_auth_tables
from config import settings

# Import core modules for authentication
from auth import (
    authenticate_user, create_access_token,
    get_user, create_user, get_user_by_email
)
from models import Token, OTPCreate, OTPVerify, AuthUser as User
from email_utils import create_otp, verify_otp, send_otp_email

# Import middleware
from middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Crowd Detection Service", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# Create auth database tables and default users (admin and regular user)
from admin import setup_default_users
create_auth_tables()
setup_default_users()

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="crowd_detection/templates")
app.mount("/static", StaticFiles(directory="crowd_detection/static"), name="static")

# Include crowd detection routes
app.include_router(crowd_detection_router, prefix="", tags=["Crowd Detection"])

# Home page - protected by middleware
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("crowd_detection.html", {"request": request})

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1  # 30 days if remember is checked, 1 day otherwise
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# Logout
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# API token endpoint for programmatic access
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
