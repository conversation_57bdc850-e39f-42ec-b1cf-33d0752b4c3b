import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Helmet Detection database settings
    DB_USER: str = "root"
    DB_PASSWORD: str = "Navaneeth%402002"
    DB_HOST: str = "localhost"
    DB_NAME: str = "helmet_detection_db"
    DB_PORT: int = 3306

    # Authentication service URL
    AUTH_SERVICE_URL: str = "http://localhost:8000"

    class Config:
        env_file = ".env"

settings = Settings()
