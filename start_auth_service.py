#!/usr/bin/env python3
"""
Startup script for Auth Service
Run this to start the authentication service on port 8000
"""

import uvicorn
import sys
import os

# Add the auth_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'auth_service'))

if __name__ == "__main__":
    print("Starting Auth Service on http://localhost:8000")
    print("This service handles user authentication and authorization")
    print("Access login page at: http://localhost:8000/login")
    print("=" * 50)
    
    uvicorn.run(
        "auth_service.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
