#!/usr/bin/env python3
"""
Startup script for Quality Control Module
Run this to start the quality control module on port 8004
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Quality Control Module on http://localhost:8004")
    print("This module handles quality control and inspection")
    print("Access module at: http://localhost:8004")
    print("Login at: http://localhost:8004/login")
    print("=" * 50)
    
    uvicorn.run(
        "quality_control.app.main:app",
        host="0.0.0.0",
        port=8004,
        reload=True,
        log_level="info"
    )
