import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Auth service settings
    AUTH_SERVICE_URL: str = "http://localhost:8000"
    
    # Service URLs
    FACE_RECOGNITION_SERVICE_URL: str = "http://localhost:8001"
    CROWD_DETECTION_SERVICE_URL: str = "http://localhost:8002"
    HELMET_DETECTION_SERVICE_URL: str = "http://localhost:8003"
    QUALITY_CONTROL_SERVICE_URL: str = "http://localhost:8004"
    
    # Security settings (for token verification)
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    class Config:
        env_file = ".env"

settings = Settings()
