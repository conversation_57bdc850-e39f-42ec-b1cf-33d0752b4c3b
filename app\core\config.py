
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):# Auth database settings (vigilanteye)
    AUTH_DB_USER: str = "root"
    AUTH_DB_PASSWORD: str = "Navaneeth%402002"
    AUTH_DB_HOST: str = "localhost"
    AUTH_DB_NAME: str = "vigilanteye"
    AUTH_DB_PORT: int = 3306

    # Security settings
    SECRET_KEY: str = "your-secret-key-change-this-in-production"  # Change this in production!
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Email settings for OTP verification
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    EMAIL_SENDER: str = "<EMAIL>"   #<EMAIL>
    EMAIL_PASSWORD: str = "lljb owqm pyrj pwnq"  # qkav mdkk aukp jlpo  
    ADMIN_EMAIL: str = "<EMAIL>"  # Email to receive OTP codes
    OTP_EXPIRY_MINUTES: int = 10  # OTP expiry time in minutes

    class Config:
        env_file = ".env"

settings = Settings()