version: '3.8'

services:
  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: auth_service/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - AUTH_DB_HOST=mysql
      - AUTH_DB_USER=root
      - AUTH_DB_PASSWORD=Navaneeth%402002
      - AUTH_DB_NAME=vigilanteye
    depends_on:
      - mysql
    volumes:
      - ./auth_service:/app
    command: uvicorn app:app --host 0.0.0.0 --port 8000 --reload

  # Face Recognition Service
  face-recognition-service:
    build:
      context: .
      dockerfile: face_recognition_service/Dockerfile
    ports:
      - "8001:8001"
    environment:
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASSWORD=Navaneeth%402002
      - DB_NAME=face_recognition_db
      - AUTH_SERVICE_URL=http://auth-service:8000
    depends_on:
      - mysql
      - auth-service
    volumes:
      - ./face_recognition_service:/app
      - ./Dataset:/app/Dataset
      - ./static:/app/static
    command: uvicorn app:app --host 0.0.0.0 --port 8001 --reload

  # Crowd Detection Service
  crowd-detection-service:
    build:
      context: .
      dockerfile: crowd_detection_service/Dockerfile
    ports:
      - "8002:8002"
    environment:
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASSWORD=Navaneeth%402002
      - DB_NAME=crowd_detection_db
      - AUTH_SERVICE_URL=http://auth-service:8000
    depends_on:
      - mysql
      - auth-service
    volumes:
      - ./crowd_detection_service:/app
    command: uvicorn app:app --host 0.0.0.0 --port 8002 --reload

  # Helmet Detection Service
  helmet-detection-service:
    build:
      context: .
      dockerfile: helmet_detection_service/Dockerfile
    ports:
      - "8003:8003"
    environment:
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASSWORD=Navaneeth%402002
      - DB_NAME=helmet_detection_db
      - AUTH_SERVICE_URL=http://auth-service:8000
    depends_on:
      - mysql
      - auth-service
    volumes:
      - ./helmet_detection_service:/app
    command: uvicorn app:app --host 0.0.0.0 --port 8003 --reload

  # Quality Control Service
  quality-control-service:
    build:
      context: .
      dockerfile: quality_control_service/Dockerfile
    ports:
      - "8004:8004"
    environment:
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASSWORD=Navaneeth%402002
      - DB_NAME=quality_control_db
      - AUTH_SERVICE_URL=http://auth-service:8000
    depends_on:
      - mysql
      - auth-service
    volumes:
      - ./quality_control_service:/app
    command: uvicorn app:app --host 0.0.0.0 --port 8004 --reload

  # Dashboard Service
  dashboard-service:
    build:
      context: .
      dockerfile: dashboard_service/Dockerfile
    ports:
      - "8005:8005"
    environment:
      - AUTH_SERVICE_URL=http://auth-service:8000
      - FACE_RECOGNITION_SERVICE_URL=http://face-recognition-service:8001
      - CROWD_DETECTION_SERVICE_URL=http://crowd-detection-service:8002
      - HELMET_DETECTION_SERVICE_URL=http://helmet-detection-service:8003
      - QUALITY_CONTROL_SERVICE_URL=http://quality-control-service:8004
    depends_on:
      - auth-service
    volumes:
      - ./dashboard_service:/app
    command: uvicorn app:app --host 0.0.0.0 --port 8005 --reload

  # MySQL Database
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: Navaneeth%402002
      MYSQL_DATABASE: vigilanteye
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql

volumes:
  mysql_data:
