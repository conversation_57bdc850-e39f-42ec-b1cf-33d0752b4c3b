#!/usr/bin/env python3
"""
Startup script for Helmet Detection Service
Run this to start the helmet detection service on port 8003
"""

import uvicorn
import sys
import os

# Add the helmet_detection_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'helmet_detection_service'))

if __name__ == "__main__":
    print("Starting Helmet Detection Service on http://localhost:8003")
    print("This service handles helmet detection and safety monitoring")
    print("Access service at: http://localhost:8003")
    print("=" * 50)
    
    uvicorn.run(
        "helmet_detection_service.app:app",
        host="0.0.0.0",
        port=8003,
        reload=True,
        log_level="info"
    )
