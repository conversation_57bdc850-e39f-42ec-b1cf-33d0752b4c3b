from sqlalchemy import create_engine
from app.core.config import settings
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Create declarative bases for both databases
Base = declarative_base()  # For surveillance_system
AuthBase = declarative_base()  # For vigilanteye auth database

# Main database URL (surveillance_system)
SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
)

# Auth database URL (vigilanteye)
AUTH_DATABASE_URL = (
    f"mysql+pymysql://{settings.AUTH_DB_USER}:{settings.AUTH_DB_PASSWORD}@{settings.AUTH_DB_HOST}:{settings.AUTH_DB_PORT}/{settings.AUTH_DB_NAME}"
)

# Create engines
engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_size=10, max_overflow=20)
auth_engine = create_engine(AUTH_DATABASE_URL, pool_size=5, max_overflow=10)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AuthSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=auth_engine)

# Function to create all tables in main database
def create_tables():
    Base.metadata.create_all(bind=engine)

# Function to create all tables in auth database
def create_auth_tables():
    AuthBase.metadata.create_all(bind=auth_engine)

# Main database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Auth database dependency
def get_auth_db():
    db = AuthSessionLocal()
    try:
        yield db
    finally:
        db.close()
