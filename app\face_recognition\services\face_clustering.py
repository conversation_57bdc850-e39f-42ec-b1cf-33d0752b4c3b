import numpy as np
import hdbscan
from sqlalchemy.orm import Session
from app.face_recognition.models import Unknown
import logging
from typing import List, Dict, Any, <PERSON>ple
import uuid
import os
import cv2
from mtcnn import MTCNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('FaceClustering')

class FaceClusteringService:
    """Service for clustering unknown faces using HDBSCAN algorithm."""

    def __init__(self, db: Session):
        """Initialize the face clustering service.

        Args:
            db (Session): SQLAlchemy database session
        """
        self.db = db
        self.face_detector = MTCNN()

    def detect_face(self, image_path: str) -> bool:
        """
        Check if an image contains a face.

        Args:
            image_path (str): Path to the image file

        Returns:
            bool: True if a face is detected, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(image_path):
                logger.warning(f"Image file not found: {image_path}")
                return False

            # Read the image
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to read image: {image_path}")
                return False

            # Detect faces
            faces = self.face_detector.detect_faces(image)

            # Return True if at least one face is detected
            return len(faces) > 0
        except Exception as e:
            logger.error(f"Error detecting face in {image_path}: {str(e)}")
            return False

    def get_unknown_encodings(self) -> Tuple[List[np.ndarray], List[int], List[str]]:
        """Fetch all unknown face encodings from the database.

        Returns:
            Tuple containing:
            - List of face encodings as numpy arrays
            - List of corresponding unknown IDs
            - List of corresponding persistent IDs
        """
        unknowns = self.db.query(Unknown).all()

        # Skip unknowns with empty encodings (should not happen, but just in case)
        valid_unknowns = [u for u in unknowns if u.encoding and len(u.encoding) > 0]

        if not valid_unknowns:
            logger.warning("No valid unknown face encodings found in the database")
            return [], [], []

        # Extract encodings, IDs and persistent IDs
        encodings = [np.array(u.encoding) for u in valid_unknowns]
        ids = [u.id for u in valid_unknowns]
        persistent_ids = [u.persistent_id for u in valid_unknowns]

        logger.info(f"Retrieved {len(encodings)} unknown face encodings from the database")
        return encodings, ids, persistent_ids

    def cluster_faces(self, min_cluster_size: int = 2, min_samples: int = None) -> Dict[str, Any]:
        """Cluster unknown faces using HDBSCAN algorithm.

        HDBSCAN automatically determines the optimal clustering structure, including the number of clusters.
        Before clustering, this method checks all images for faces and removes those without faces.

        Args:
            min_cluster_size (int): Minimum number of samples in a cluster. Default is 2.
                                   Increasing this value will result in fewer, larger clusters.
            min_samples (int): Optional. If None (default), it will be set equal to min_cluster_size.
                              Controls the conservativeness of clustering. Higher values make the algorithm
                              more conservative and less likely to find clusters.

        Returns:
            Dict containing clustering results:
            - 'success': Boolean indicating if clustering was successful
            - 'message': Status message
            - 'clusters': Dict mapping cluster IDs to lists of unknown IDs
            - 'noise': List of unknown IDs that were classified as noise
            - 'removed_images': Number of images removed due to no face detected
        """
        # First, check all unknown images for faces and remove those without faces
        removed_images = self._remove_images_without_faces()
        logger.info(f"Removed {removed_images} images without faces")

        # Get encodings and IDs
        encodings, unknown_ids, persistent_ids = self.get_unknown_encodings()

        if not encodings:
            return {
                'success': False,
                'message': 'No unknown face encodings found',
                'clusters': {},
                'noise': [],
                'removed_images': removed_images
            }

        try:
            # Convert list of encodings to a 2D numpy array
            X = np.array(encodings)

            # Initialize and fit HDBSCAN
            # If min_samples is None, HDBSCAN will set it equal to min_cluster_size
            # which is a good default for most cases
            # HDBSCAN doesn't directly support cosine metric, so we'll use euclidean
            # which works well for normalized face embeddings

            # For small datasets, we need to be more lenient with clustering parameters
            # Ensure min_cluster_size is not larger than the number of samples
            adjusted_min_cluster_size = min(min_cluster_size, len(encodings))
            if adjusted_min_cluster_size < min_cluster_size:
                logger.info(f"Adjusted min_cluster_size from {min_cluster_size} to {adjusted_min_cluster_size} based on dataset size")

            # Use a more lenient epsilon value for small datasets
            epsilon = 0.3  # Increased from 0.1 to be more lenient

            logger.info(f"Clustering {len(encodings)} faces with min_cluster_size={adjusted_min_cluster_size}, min_samples={min_samples}, epsilon={epsilon}")

            clusterer = hdbscan.HDBSCAN(
                min_cluster_size=adjusted_min_cluster_size,
                min_samples=min_samples,
                metric='euclidean',  # Use euclidean distance which works well for face embeddings
                cluster_selection_epsilon=epsilon,  # More relaxed cluster selection for small datasets
                cluster_selection_method='eom'  # 'eom' (Excess of Mass) often works better for face clustering
            )
            cluster_labels = clusterer.fit_predict(X)

            # Log detailed information about the clustering results
            unique_labels = set(cluster_labels)
            logger.info(f"Clustering produced {len(unique_labels)} unique labels: {unique_labels}")

            # Count occurrences of each label
            label_counts = {}
            for label in cluster_labels:
                if label not in label_counts:
                    label_counts[label] = 0
                label_counts[label] += 1

            logger.info(f"Label counts: {label_counts}")

            # Log the encodings to understand the data better
            logger.info(f"Encodings shape: {X.shape}")

            # Calculate pairwise distances to see how similar the faces are
            from scipy.spatial.distance import pdist, squareform
            distances = squareform(pdist(X, metric='euclidean'))

            # Log the distance matrix
            logger.info(f"Distance matrix shape: {distances.shape}")

            # Find the minimum distance between any two points
            min_dist = float('inf')
            closest_pair = (0, 0)

            for i in range(len(distances)):
                for j in range(i+1, len(distances)):
                    if distances[i][j] < min_dist:
                        min_dist = distances[i][j]
                        closest_pair = (i, j)

            logger.info(f"Closest pair: {closest_pair} with distance {min_dist}")

            # Calculate average distance between points
            avg_dist = np.mean(distances[np.triu_indices(len(distances), k=1)])
            logger.info(f"Average distance between points: {avg_dist}")

            # Try different distance thresholds
            for threshold in [0.5, 0.75, 1.0, 1.5, 2.0]:
                similar_pairs = 0
                for i in range(len(distances)):
                    for j in range(i+1, len(distances)):
                        if distances[i][j] < threshold:
                            similar_pairs += 1

                logger.info(f"Pairs with distance < {threshold}: {similar_pairs} out of {len(distances) * (len(distances) - 1) // 2}")

            # For small datasets, we need a more aggressive approach to clustering
            # If all points are noise or we have very few non-noise clusters, try a simpler approach
            if (len(unique_labels) == 1 and -1 in unique_labels) or (len(unique_labels) > 1 and len(unique_labels.difference({-1})) < 1):
                logger.info("Few or no clusters found. Using a simpler distance-based clustering approach...")

                # We already calculated distances above, now use them for clustering

                # Try a simple threshold-based clustering
                threshold = 1.0  # This is a reasonable threshold for face similarity

                # Initialize all points as noise
                simple_labels = np.array([-1] * len(cluster_labels))

                # Group points that are close to each other
                cluster_id = 0
                for i in range(len(distances)):
                    # Skip if already assigned to a cluster
                    if simple_labels[i] != -1:
                        continue

                    # Create a new cluster with this point
                    simple_labels[i] = cluster_id
                    cluster_size = 1

                    # Find all points that are close to this one
                    for j in range(len(distances)):
                        if i != j and simple_labels[j] == -1 and distances[i][j] < threshold:
                            simple_labels[j] = cluster_id
                            cluster_size += 1

                    # Only keep the cluster if it has at least 2 points
                    if cluster_size >= 2:
                        cluster_id += 1
                    else:
                        # Reset to noise if only one point
                        simple_labels[i] = -1

                # If we found any clusters, use them
                if cluster_id > 0:
                    logger.info(f"Simple clustering created {cluster_id} clusters")
                    cluster_labels = simple_labels
                else:
                    logger.info("Simple clustering found no clusters. Trying with closest pairs...")

                    # If still no clusters, just group the closest pairs
                    # Sort all pairs by distance
                    pairs = []
                    for i in range(len(distances)):
                        for j in range(i+1, len(distances)):
                            pairs.append((i, j, distances[i][j]))

                    # Sort by distance (ascending)
                    pairs.sort(key=lambda x: x[2])

                    # Take the closest pairs to form clusters
                    simple_labels = np.array([-1] * len(cluster_labels))
                    used_points = set()
                    cluster_id = 0

                    for i, j, dist in pairs:
                        # Skip if either point is already used
                        if i in used_points or j in used_points:
                            continue

                        # Create a new cluster with these two points
                        simple_labels[i] = cluster_id
                        simple_labels[j] = cluster_id
                        used_points.add(i)
                        used_points.add(j)

                        logger.info(f"Created cluster {cluster_id} with points {i} and {j} (distance: {dist:.4f})")

                        cluster_id += 1

                        # Stop after creating a few clusters
                        if cluster_id >= 2 or len(pairs) <= 1:
                            break

                    # If we found any clusters, use them
                    if cluster_id > 0:
                        logger.info(f"Pair-based clustering created {cluster_id} clusters")
                        cluster_labels = simple_labels

            # Organize results
            clusters = {}
            noise = []

            for i, (label, unknown_id) in enumerate(zip(cluster_labels, unknown_ids)):
                if label == -1:
                    # Points labeled -1 are noise
                    noise.append(unknown_id)
                else:
                    # Create a string label for the cluster
                    cluster_key = f"cluster_{label}"
                    if cluster_key not in clusters:
                        clusters[cluster_key] = []
                    clusters[cluster_key].append(unknown_id)

            # Update cluster information in the database
            self._update_cluster_info(cluster_labels, unknown_ids)

            logger.info(f"Clustering completed: {len(clusters)} clusters found, {len(noise)} noise points")

            return {
                'success': True,
                'message': f'Successfully clustered unknown faces into {len(clusters)} groups',
                'clusters': clusters,
                'noise': noise,
                'removed_images': removed_images
            }

        except Exception as e:
            logger.error(f"Error during clustering: {str(e)}")
            return {
                'success': False,
                'message': f'Error during clustering: {str(e)}',
                'clusters': {},
                'noise': [],
                'removed_images': removed_images
            }

    def _update_cluster_info(self, cluster_labels: np.ndarray, unknown_ids: List[int]) -> None:
        """Update cluster information in the database.

        Args:
            cluster_labels (np.ndarray): Array of cluster labels
            unknown_ids (List[int]): List of unknown IDs
        """
        # Generate a unique run ID for this clustering session
        clustering_run_id = str(uuid.uuid4())

        for label, unknown_id in zip(cluster_labels, unknown_ids):
            try:
                unknown = self.db.query(Unknown).filter(Unknown.id == unknown_id).first()
                if unknown:
                    # For noise points (label -1), set cluster_id to None
                    if label == -1:
                        unknown.cluster_id = None
                    else:
                        # Use a combination of run ID and label for the cluster ID
                        unknown.cluster_id = f"{clustering_run_id}_{label}"

                self.db.commit()
            except Exception as e:
                logger.error(f"Error updating cluster info for unknown ID {unknown_id}: {str(e)}")
                self.db.rollback()

    def get_clustered_unknowns(self) -> Dict[str, Any]:
        """Get unknown faces grouped by clusters.

        Returns:
            Dict containing:
            - 'clusters': Dict mapping cluster IDs to lists of unknown data
            - 'unclustered': List of unclustered unknown data
        """
        try:
            # Get all unknowns
            unknowns = self.db.query(Unknown).all()

            # Group by cluster_id
            clusters = {}
            unclustered = []

            for unknown in unknowns:
                # Skip unknowns with empty encodings
                if not unknown.encoding or len(unknown.encoding) == 0:
                    continue

                # Get image paths for this unknown
                image_paths = self._get_image_paths_for_unknown(unknown.persistent_id)

                unknown_data = {
                    'id': unknown.id,
                    'persistent_id': unknown.persistent_id,
                    'image_paths': image_paths,
                    'timestamp': unknown.timestamp.isoformat() if unknown.timestamp else None
                }

                if unknown.cluster_id:
                    if unknown.cluster_id not in clusters:
                        clusters[unknown.cluster_id] = []
                    clusters[unknown.cluster_id].append(unknown_data)
                else:
                    unclustered.append(unknown_data)

            return {
                'clusters': clusters,
                'unclustered': unclustered
            }

        except Exception as e:
            logger.error(f"Error getting clustered unknowns: {str(e)}")
            return {
                'clusters': {},
                'unclustered': []
            }

    def _remove_images_without_faces(self) -> int:
        """
        Check all unknown images for faces and remove those without faces.

        Returns:
            int: Number of images removed
        """
        folder_path = "Dataset/unknown"
        removed_count = 0

        if not os.path.exists(folder_path):
            logger.warning(f"Unknown images folder not found: {folder_path}")
            return 0

        # Get all unknown records
        unknowns = self.db.query(Unknown).all()

        # Process each unknown person
        for unknown in unknowns:
            # Get all image files for this unknown person
            image_files = [f for f in os.listdir(folder_path)
                          if f.startswith(f"unknown_{unknown.persistent_id}_")]

            # Skip if no images found
            if not image_files:
                continue

            # Check each image for faces
            valid_images = []
            for file in image_files:
                file_path = os.path.join(folder_path, file)

                # Check if the image contains a face
                has_face = self.detect_face(file_path)

                if not has_face:
                    # Remove the image file
                    try:
                        os.remove(file_path)
                        logger.info(f"Removed image without face: {file_path}")
                        removed_count += 1
                    except Exception as e:
                        logger.error(f"Error removing image {file_path}: {str(e)}")
                else:
                    valid_images.append(file)

            # If all images were removed, delete the unknown record
            if not valid_images:
                try:
                    self.db.delete(unknown)
                    self.db.commit()
                    logger.info(f"Removed unknown record {unknown.id} (persistent_id: {unknown.persistent_id}) as it had no valid images")
                except Exception as e:
                    logger.error(f"Error deleting unknown record {unknown.id}: {str(e)}")
                    self.db.rollback()

        return removed_count

    def _get_image_paths_for_unknown(self, persistent_id: str) -> List[str]:
        """Get image paths for an unknown person.

        Args:
            persistent_id (str): Persistent ID of the unknown person

        Returns:
            List of image paths
        """
        folder_path = "Dataset/unknown"
        images = []

        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if file.startswith(f"unknown_{persistent_id}_"):
                    image_path = os.path.join("/", folder_path, file)
                    if image_path not in images:
                        images.append(image_path)

        return images
