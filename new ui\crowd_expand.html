<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - Video Monitoring Dashboard</title>
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
      --panel-bg: #ffffff;
      --border-radius: 8px;
      --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
      --primary-light: rgba(27, 165, 123, 0.1);
      --primary-color: #1ba57b;
      --transition: all 0.3s ease;
      --text-muted: #7a7a7a;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto;
    }
    
    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
      /* color: #2ecc71; */
    }
    
    .logo svg {
      margin-right: 12px;
    }
    
    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
    }
    
    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }
    
    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }
    
    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }
    
    .system-status {
      margin-top: auto;
      padding: 15px 25px;
      font-size: 13px;
      background-color: rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #4caf50;
    }
    
    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      margin-left: 280px;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }
    
    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }
    
    .btn svg {
      margin-right: 8px;
    }
    
    .btn-primary {
      background-color: #1565c0;
      color: white;
    }
    
    .btn-primary:hover {
      background-color:  #d01736;
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }
    
    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }
    
    
    
    .blue-icon {
      color: #1e88e5;
    }

    
    .orange-icon {
      color: #ff9800;
    }
    
    .yellow-icon {
      color: #ffc107;
    }
    
    .purple-bg {
      background-color: rgba(156, 39, 176, 0.15);
    }
    
    .purple-icon {
      color: #9c27b0;
    }
    
    .red-bg {
      background-color: rgba(244, 67, 54, 0.15);
    }
    
    .red-icon {
      color: #f44336;
    } 

    .content-area {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
        flex: 1;
    }
    
    .panel {
        background-color: var(--panel-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #eaeaea;
    }

    .panel-title {
        font-size: 18px;
        font-weight: 500;
    }

    .panel-actions {
        display: flex;
        gap: 12px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--primary-light);
        color: var(--primary-color);
        cursor: pointer;
        transition: var(--transition);
    }

    .action-btn:hover {
        background-color: rgba(27, 165, 123, 0.2);
    }

    .panel-body {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Webcam view */
    .webcam-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;
        background-color: #f0f0f0;
        border-radius: 4px;
        min-height: 400px;
    }

    .webcam-placeholder {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var(--text-muted);
    }

    .webcam-placeholder i {
        font-size: 40px;
        margin-bottom: 16px;
    }

    .camera-controls {
        margin-top: 16px;
        display: flex;
        gap: 12px;
    }

    /* Observations panel */
    .observations-list {
        flex: 1;
        overflow-y: auto;
    }

    .observation-item {
        padding: 12px 0;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eaeaea;
    }

    .observation-item:last-child {
        border-bottom: none;
    }

    .observation-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
    }
    
   .blue-bg {
      background-color: rgba(30, 136, 229, 0.15);
    }
    
    .blue-icon {
      color: #1e88e5;
    }
    
    .orange-bg {
      background-color: rgba(255, 152, 0, 0.15);
    }
    
    .orange-icon {
      color: #ff9800;
    }
    
    .yellow-bg {
      background-color: rgba(255, 193, 7, 0.15);
    }
    
    .yellow-icon {
      color: #ffc107;
    }
    
    .purple-bg {
      background-color: rgba(156, 39, 176, 0.15);
    }
    
    .purple-icon {
      color: #9c27b0;
    }
    
    .red-bg {
      background-color: rgba(244, 67, 54, 0.15);
    }
    
    .red-icon {
      color: #f44336;
    }

    .observation-details {
        flex: 1;
    }

    .observation-title {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .observation-info {
        display: flex;
        font-size: 12px;
        color: var(--text-muted);
    }

    .observation-location {
        margin-right: 12px;
    }

    .observation-time {
        margin-left: auto;
    }

    /* Status indicators and badges */
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-active {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4CAF50;
    }

    .status-offline {
        background-color: rgba(244, 67, 54, 0.1);
        color: #F44336;
    }

    .status-warning {
        background-color: rgba(255, 152, 0, 0.1);
        color: #FF9800;
    }

    .controls {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    
    .control-btn {
      background-color: #f5f5f5;
      border: none;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .control-btn:hover {
      background-color: #e0e0e0;
    }

    /* New styles for crowd monitoring */
    .crowd-metrics {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .metric-card {
      background: #fff;
      border-radius: 8px;
      padding: 14px 16px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
    }

    .metric-card:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .metric-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray);
    }

    .metric-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-value {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 4px;
      color: var(--dark);
    }

    .metric-subtitle {
      font-size: 13px;
      color: var(--text-muted);
    }

    .metric-counts {
      display: flex;
      margin-top: 10px;
      gap: 15px;
    }

    .count-item {
      flex: 1;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
      text-align: center;
    }

    .count-label {
      font-size: 12px;
      color: var(--text-muted);
      margin-bottom: 4px;
    }

    .count-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--dark);
    }

    .green-count {
      color: #2e7d32;
    }

    .amber-count {
      color: #ff9800;
    }

    .red-count {
      color: #d32f2f;
    }

    .metric-trend {
      display: flex;
      align-items: center;
      font-size: 13px;
      margin-top: 8px;
    }

    .trend-up {
      color: #2e7d32;
    }

    .trend-down {
      color: #d32f2f;
    }

    .trend-icon {
      margin-right: 4px;
    }

    @media (max-width: 1024px) {
        .content-area {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        height: auto;
        position: relative;
      }
      
      .content {
        margin-left: 0;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>
    <a href="/" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>
    <a href="#" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect>
        <line x1="7" y1="2" x2="7" y2="22"></line>
        <line x1="17" y1="2" x2="17" y2="22"></line>
        <line x1="2" y1="12" x2="22" y2="12"></line>
        <line x1="2" y1="7" x2="7" y2="7"></line>
        <line x1="2" y1="17" x2="7" y2="17"></line>
        <line x1="17" y1="17" x2="22" y2="17"></line>
        <line x1="17" y1="7" x2="22" y2="7"></line>
      </svg>
      Monitoring
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
      </svg>
      Focus Areas
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
        <circle cx="12" cy="13" r="4"></circle>
      </svg>
      Camera Management
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polygon points="18 13 20 15 22 13"></polygon>
        <path d="M15 15l-6-6"></path>
        <path d="M13 15h2v2"></path>
        <path d="M7 7l0 2 -2 0"></path>
        <path d="M8.5 17.5L5 21"></path>
        <path d="M18.5 17.5L22 21"></path>
        <path d="M2 3l20 18"></path>
      </svg>
      Alert Management
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
      </svg>
      Analytics
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      </svg>
      Event History
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
      User Access
    </a>
    <div class="system-status">
      <div><span class="status-indicator"></span> System Online</div>
      <div style="margin-top: 5px; opacity: 0.7;">v2.3.5 | IP: 127.0.0.1:8000</div>
    </div>
  </div>

  <div class="content">
    <div class="header">
      <div class="page-title">
        Video Monitoring
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          Settings
        </button>
        <button class="btn btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Stop Monitoring
        </button>
      </div>
    </div>


    <div class="content-area">
        <!-- Webcam View Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Webcam View</h2>
                <div class="controls">
                    <button class="control-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                    </svg>
                    </button>
                    <button class="control-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 15s1-1 4-1 5 2 5 2V2s-1 1-5 1-4-1-4-1v12z"></path>
                        <path d="M0 15h15"></path>
                        <path d="M0 7h15"></path>
                    </svg>
                    </button>
                </div>

            </div>
            <div class="panel-body">
                <div class="webcam-container">
                    <div class="webcam-placeholder">
                        <i class="fas fa-camera"></i>
                        
                        <p>Camera feed not active</p>
                        <div class="camera-controls">
                            <button class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                Start Stream
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Updated Live Observations Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Live Observations</h2>
                <div class="panel-actions">
                    <div class="status-badge status-active">
                        Live
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <div class="crowd-metrics">
                    <!-- Camera 1 -->
                    <div class="metric-card">
                        <div class="metric-header">
                            <div class="metric-title">Camera Name</div>
                            <div class="metric-icon blue-bg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="blue-icon">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                                    <circle cx="12" cy="13" r="4"></circle>
                                </svg>
                            </div>
                        </div>
                        <div class="metric-value">Main Entrance</div>
                        <div class="metric-subtitle">Updated just now</div>
                        
                        <div class="metric-counts">
                            <div class="count-item">
                                <div class="count-label">Total Count</div>
                                <div class="count-value" id="total-count-1">42</div>
                            </div>
                            <div class="count-item">
                                <div class="count-label">Region 1</div>
                                <div class="count-value green-count" id="region1-count-1">28</div>
                            </div>
                            <div class="count-item">
                                <div class="count-label">Region 2</div>
                                <div class="count-value amber-count" id="region2-count-1">14</div>
                            </div>
                        </div>
                        
                    </div>

                    <div style="margin-top: 15px; text-align: center;">
                            <button class="btn btn-primary" style="width: 100%;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                    <polyline points="7 3 7 8 15 8"></polyline>
                                </svg>
                                Save Changes
                            </button>
                        </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>

