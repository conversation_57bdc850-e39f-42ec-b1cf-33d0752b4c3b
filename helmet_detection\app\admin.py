from sqlalchemy.orm import Session
from auth import get_password_hash, get_user
from database import AuthSessionLocal, create_auth_tables
from models import AuthUser as User

def create_admin_user():
    """
    Create an admin user if it doesn't already exist.
    This function should be called when the application starts.
    """
    # Ensure tables exist
    create_auth_tables()

    # Create a session
    db = AuthSessionLocal()

    try:
        # Check if admin already exists
        admin = get_user(db, "admin")
        if admin:
            # Ensure the admin user has the admin role
            if admin.role != "admin":
                admin.role = "admin"
                db.commit()
                print("Updated existing admin user with admin role")
            else:
                print("Admin user already exists with admin role")
            return

        # Create admin user
        admin_user = User(
            username="admin",
            email="<EMAIL>",  # Change this to a real email
            full_name="Administrator",
            hashed_password=get_password_hash("admin123"),  # Change this password!
            role="admin",  # Set role to admin
            disabled=False
        )

        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)

        print("Admin user created successfully")
    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

def create_regular_user():
    """
    Create a regular user if it doesn't already exist.
    This can be called when the application starts or as needed.
    """
    # Ensure tables exist
    create_auth_tables()

    # Create a session
    db = AuthSessionLocal()

    try:
        # Check if regular user already exists
        regular_user = get_user(db, "user")
        if regular_user:
            # Ensure the user has the user role
            if regular_user.role != "user":
                regular_user.role = "user"
                db.commit()
                print("Updated existing regular user with user role")
            else:
                print("Regular user already exists with user role")
            return

        # Create regular user
        user = User(
            username="user",
            email="<EMAIL>",  # Change this to a real email
            full_name="Regular User",
            hashed_password=get_password_hash("user123"),  # Change this password!
            role="user",  # Set role to user
            disabled=False
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        print("Regular user created successfully")
    except Exception as e:
        print(f"Error creating regular user: {e}")
        db.rollback()
    finally:
        db.close()

def setup_default_users():
    """
    Set up both admin and regular users.
    This function can be called during application startup.
    """
    create_admin_user()
    create_regular_user()

if __name__ == "__main__":
    setup_default_users()
