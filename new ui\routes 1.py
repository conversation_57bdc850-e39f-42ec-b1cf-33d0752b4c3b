from fastapi import APIRouter,WebSocket, Request, HTTPException,UploadFile,File
from typing import Optional
from fastapi.templating import Jin<PERSON>2Templates
from app.helmet_detection.utils import HelmetDetection
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
import cv2
import shutil
import os

router = APIRouter()

templates = Jinja2Templates(directory="app/helmet_detection/templates")

@router.get("/helmet_detection")
async def show_crowd_detection_page(request: Request):
    return templates.TemplateResponse("helmet_detection.html", {"request": request})

@router.get("/expand")
async def show_expand_page(request: Request):
    return templates.TemplateResponse("expand.html", {"request": request})

@router.get("/upload")
async def show_expand_page(request: Request):
    return templates.TemplateResponse("upload.html", {"request": request})

# Initialize Helmetdetection without starting it
helmet_stream = HelmetDetection(object_model="./app/helmet_detection/models/best.pt",pose_model="./app/helmet_detection/models/yolov8s-pose.pt",conf_value= 0.5)


def shutdown_event():
    helmet_stream.stop()


# API to start video processing
@router.post("/start-helmet_detection")
async def start_stream(request: Request, file: Optional[UploadFile] = File(None)):
    if not helmet_stream.running:
        camera_details = load_cameras()
        test_video_path = None

        if file:
            save_path = f"./app/helmet_detection/uploads/{file.filename}"
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            test_video_path = save_path

        camera_details = load_cameras()
        if not camera_details:
            return {"status": "error", "message": "No cameras configured."}
        print(f"Loaded Cameras: {camera_details}")

        helmet_stream.start(camera_details, test_video_path=test_video_path)

    return {"status": "helmet_stream started"}

# API to stop video processing
@router.post("/stop-stream")
async def stop_stream():
    if helmet_stream.running:
        helmet_stream.stop()
        helmet_stream.helmet_frames = []
    return {"status": "helmet_stream stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            if helmet_stream.running:
                for idx, frame in enumerate(helmet_stream.helmet_frames):
                    if frame is not None:
                        with helmet_stream.locks[idx]:
                            warnings = helmet_stream.warning_count[idx]
                            cam_name = helmet_stream.camera_name[idx]
                            helmet_frame = helmet_stream.helmet_frames[idx]

                        # cameraId is 1-indexed for frontend compatibility
                        await websocket.send_text(f"{idx+1}:{cam_name}:{warnings['person_count']}:{warnings['no_helmet_count']}:{warnings['no_vest_count']}:{warnings['no_gloves_count']}")
                        print(f"Sending frame for camera {cam_name}")
                        print(f"Warnings: {warnings}")
                        await websocket.send_bytes(helmet_frame)
                await asyncio.sleep(0.03)
            else:
                await asyncio.sleep(0.5)
    except Exception as e:
        print("WebSocket disconnected:", e)

    
from urllib.parse import unquote
import numpy as np
from pydantic import BaseModel

# class CameraStartRequest(BaseModel):
#     camera_name: str


# @router.post("/start-camera")
# async def start_camera(req: CameraStartRequest):
#     camera_name = req.camera_name
#     if not helmet_stream.running:
#         all_cameras = load_cameras()
        
#         # Find the camera by display name
#         camera_found = False
#         for cam_key, cam_value in all_cameras.items():
#             if cam_key.lower() == camera_name.lower() or cam_key == camera_name:
#                 single_camera = {cam_key: cam_value}
#                 camera_found = True
#                 break
        
#         if not camera_found:
#             # If exact match not found, try to map display name to camera name
#             if "Camera 1" in camera_name:
#                 # Find the first camera in the list
#                 first_cam = next(iter(all_cameras.items()))
#                 single_camera = {first_cam[0]: first_cam[1]}
#             elif "Camera 2" in camera_name:
#                 # Find the second camera in the list
#                 if len(all_cameras) >= 2:
#                     second_cam = list(all_cameras.items())[1]
#                     single_camera = {second_cam[0]: second_cam[1]}
#                 else:
#                     return {"status": "error", "message": "Camera not found"}
#             elif "Camera 3" in camera_name:
#                 # Find the third camera in the list
#                 if len(all_cameras) >= 3:
#                     third_cam = list(all_cameras.items())[2]
#                     single_camera = {third_cam[0]: third_cam[1]}
#                 else:
#                     return {"status": "error", "message": "Camera not found"}
#             elif "Camera 4" in camera_name:
#                 # Find the fourth camera in the list
#                 if len(all_cameras) >= 4:
#                     fourth_cam = list(all_cameras.items())[3]
#                     single_camera = {fourth_cam[0]: fourth_cam[1]}
#                 else:
#                     return {"status": "error", "message": "Camera not found"}
#             else:
#                 # If no mapping found, use the first camera
#                 first_cam = next(iter(all_cameras.items()))
#                 single_camera = {first_cam[0]: first_cam[1]}
        
#         helmet_stream.start(single_camera)
#         return {"status": "single camera started", "camera": camera_name}
#     return {"status": "already running"}


# @router.post("/stop-camera")
# async def stop_camera(req: CameraStartRequest):
#     if helmet_stream.running:
#         helmet_stream.stop()
#         return {"status": "success", "message": "Camera stopped"}
#     return {"status": "not running", "message": "Camera was not running"}


@router.websocket("/ws/{camera_id}")
async def helmet_ws_camera_specific(websocket: WebSocket, camera_id: str):
    from urllib.parse import unquote
    camera_id = unquote(camera_id).strip()
    await websocket.accept()
    print(f"[DEBUG] WebSocket connected for camera_id = '{camera_id}'")
    
    # Send initial confirmation message
    await websocket.send_text(f"Connected to camera: {camera_id}")
    
    frame_count = 0
    try:
        while True:
            if helmet_stream.running:
                frame_sent = False
                for idx, cam_name in enumerate(helmet_stream.camera_name):
                    # Try to match by exact name or by display name pattern
                    if (cam_name.strip().lower() == camera_id.strip().lower() or
                        (camera_id.startswith("Camera 1") and idx == 0) or
                        (camera_id.startswith("Camera 2") and idx == 1) or
                        (camera_id.startswith("Camera 3") and idx == 2) or
                        (camera_id.startswith("Camera 4") and idx == 3)):
                        
                        with helmet_stream.locks[idx]:
                            frame = helmet_stream.helmet_frames[idx]
                            warnings = helmet_stream.warning_count[idx]
                        
                        if frame is not None and warnings is not None:
                            # First send the metadata - using the same format as in routes_original.py
                            metadata = f"{idx+1}:{cam_name}:{warnings['person_count']}:{warnings['no_helmet_count']}:{warnings['no_vest_count']}:{warnings['no_gloves_count']}"
                            await websocket.send_text(metadata)
                            print(f"[DEBUG] Sending metadata: {metadata}")
                            
                            # Then send the frame
                            if isinstance(frame, bytes):
                                # If frame is already encoded as bytes, send it directly
                                await websocket.send_bytes(frame)
                                print(f"[DEBUG] Sent already-encoded frame for {camera_id}, size: {len(frame)} bytes")
                            else:
                                # Otherwise encode it
                                success, buffer = cv2.imencode('.jpg', frame)
                                if success:
                                    frame_bytes = buffer.tobytes()
                                    await websocket.send_bytes(frame_bytes)
                                    print(f"[DEBUG] Sent newly-encoded frame for {camera_id}, size: {len(frame_bytes)} bytes")
                                else:
                                    print(f"[DEBUG] Failed to encode frame for {camera_id}")
                            
                            frame_count += 1
                            frame_sent = True
                        else:
                            print(f"[DEBUG] Frame or warnings is None for camera {cam_name}")
                
                if not frame_sent:
                    print(f"[DEBUG] No matching camera found for {camera_id}")
                    # Send a heartbeat message to keep connection alive
                    await websocket.send_text("No matching camera found")
                
                # Use a shorter sleep time for better responsiveness
                await asyncio.sleep(0.03)
            else:
                print(f"[DEBUG] helmet_stream not running")
                await asyncio.sleep(0.5)
    except Exception as e:
        print(f"WebSocket connection closed for {camera_id}: {e}")
    finally:
        print(f"[DEBUG] WebSocket connection ended for {camera_id}, sent {frame_count} frames")

@router.post("/upload-video")
async def upload_video(request: Request, file: UploadFile = File(...)):
    camera_details = load_cameras()
    test_video_path = None

    if file:
        save_path = f"./app/helmet_detection/uploads/{file.filename}"
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        test_video_path = save_path

    # Always stop the stream before starting a new one
    if helmet_stream.running:
        helmet_stream.stop()

    helmet_stream.start(camera_details, test_video_path=test_video_path)
    return {"status": "helmet_stream restarted with new video"}



#--------------------------------------------------------------ADD CAMERFAA---------------------------------------------------

# Path to the cameras.json file
CAMERAS_FILE_PATH = "app/crowd_detection/cameras.json"


# Load cameras data from the file if it exists
def load_cameras():
    if Path(CAMERAS_FILE_PATH).exists():
        with open(CAMERAS_FILE_PATH, "r") as file:
            return json.load(file)
    return {}

# Save cameras data to the file
def save_cameras(cameras_data):
    with open(CAMERAS_FILE_PATH, "w") as file:
        json.dump(cameras_data, file, indent=4)

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData):
    cameras = load_cameras()
    
    # Check if the camera name already exists
    if camera_data.cameraName in cameras:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists in the values
    for existing_camera, existing_url in cameras.items():
        if existing_url[0] == camera_data.rtspUrl:
            # Return the camera name using the same URL
            return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_camera}"}
        

    # Add new camera with empty ROI coordinates
    cameras[camera_data.cameraName] = [camera_data.rtspUrl,[]]
    
    # Save the updated data
    save_cameras(cameras)
    
    return {"status": "success", "message": "Camera added successfully"}


# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str):
    cameras = load_cameras()

    if camera_name not in cameras:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Remove the camera
    del cameras[camera_name]
    
    # Save the updated camera list
    save_cameras(cameras)
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}


@router.get("/get-cameras")
async def get_cameras():
    return load_cameras()


