from fastapi import Request, status
from fastapi.responses import RedirectResponse
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from app.face_recognition.config import settings
from app.core.models import User
from app.face_recognition.database import AuthSessionLocal

async def auth_middleware(request: Request, call_next):
    # Paths that don't require authentication
    public_paths = ["/login", "/register", "/request-otp", "/verify-otp", "/static", "/token", "/favicon.ico"]

    # Check if the path is public
    for path in public_paths:
        if request.url.path.startswith(path):
            return await call_next(request)

    # Check for token in cookies
    token = request.cookies.get("access_token")

    if not token:
        # Redirect to login if no token
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)

    try:
        # Strip the "Bearer " prefix if present
        if token.startswith("Bearer "):
            token = token[7:]

        # Validate the token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username = payload.get("sub")

        if not username:
            return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)

        # Get the user from auth database to verify they exist
        db = AuthSessionLocal()
        user = db.query(User).filter(User.username == username).first()
        db.close()

        if not user or user.disabled:
            return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)

        # Add user info to request state for use in routes
        request.state.user = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role or "user"  # Default to "user" if role is None
        }

    except JWTError:
        # Invalid token, redirect to login
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)

    # Token is valid, continue with the request
    response = await call_next(request)
    return response