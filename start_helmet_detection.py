#!/usr/bin/env python3
"""
Startup script for Helmet Detection Module
Run this to start the helmet detection module on port 8003
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Helmet Detection Module on http://localhost:8003")
    print("This module handles helmet detection and safety monitoring")
    print("Access module at: http://localhost:8003")
    print("Login at: http://localhost:8003/login")
    print("=" * 50)
    
    uvicorn.run(
        "helmet_detection.app.main:app",
        host="0.0.0.0",
        port=8003,
        reload=True,
        log_level="info"
    )
