import cv2
import numpy as np
import threading
import time
import queue
from typing import Dict, List, Optional
import logging
from concurrent.futures import ThreadPoolExecutor
from ultralytics import YOL<PERSON>
from database import SessionLocal
from models import Detection, Alert
from datetime import datetime, timezone

# Set up logging
logger = logging.getLogger(__name__)

class HelmetDetection:
    def __init__(self, object_model: str, pose_model: str, conf_value: float = 0.5):
        """
        Initialize Helmet Detection system
        
        Args:
            object_model: Path to object detection model
            pose_model: Path to pose detection model  
            conf_value: Confidence threshold for detections
        """
        self.object_model_path = object_model
        self.pose_model_path = pose_model
        self.conf_threshold = conf_value
        
        # Initialize models
        try:
            self.object_model = YOLO(object_model)
            self.pose_model = YOLO(pose_model)
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            self.object_model = None
            self.pose_model = None
        
        # System state
        self.running = False
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        
        # Camera management
        self.cap_devices = []
        self.frame_queues = []
        self.helmet_frames = []
        self.camera_threads = []
        self.process_threads = []
        self.camera_name = []
        self.warning_count = []
        
        # Threading
        self.lock = threading.Lock()
        
        # Detection tracking
        self.detection_history = {}
        self.alert_cooldown = {}  # Prevent spam alerts
        self.cooldown_duration = 30  # seconds
        
    def start(self, camera_details: Dict, test_video_path: Optional[str] = None):
        """Start helmet detection system"""
        if self.running:
            return
            
        if not self.object_model or not self.pose_model:
            logger.error("Models not loaded properly")
            return
            
        # Clear previous state
        self.stop_connections()
        
        # Initialize cameras
        if test_video_path:
            # Use test video
            self.camera_name = ["Test Video"]
            self._init_test_video(test_video_path)
        else:
            # Use camera details
            self._init_cameras(camera_details)
        
        if not self.cap_devices:
            logger.error("No cameras initialized")
            return
            
        # Initialize frames and warning counts
        self.helmet_frames = [None] * len(self.cap_devices)
        self.warning_count = [{"person_count": 0, "no_helmet_count": 0, "no_vest_count": 0, "no_gloves_count": 0} for _ in self.cap_devices]
        
        self.running = True
        
        # Start processing threads
        for idx, cap in enumerate(self.cap_devices):
            read_thread = self.thread_pool.submit(self.read_frames, idx, cap)
            process_thread = self.thread_pool.submit(self.process_frames, idx)
            self.camera_threads.append(read_thread)
            self.process_threads.append(process_thread)
            
        logger.info(f"Helmet detection started with {len(self.cap_devices)} cameras")
    
    def _init_test_video(self, video_path: str):
        """Initialize with test video"""
        try:
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
                self.frame_queues.append(queue.Queue(maxsize=10))
                logger.info(f"Test video loaded: {video_path}")
            else:
                logger.error(f"Failed to load test video: {video_path}")
        except Exception as e:
            logger.error(f"Error loading test video: {e}")
    
    def _init_cameras(self, camera_details: Dict):
        """Initialize cameras from camera details"""
        for cam_name, cam_info in camera_details.items():
            try:
                cam_url = cam_info[0]
                if cam_url.isdigit():
                    cam_url = int(cam_url)
                    
                cap = cv2.VideoCapture(cam_url)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                if cap.isOpened():
                    self.cap_devices.append(cap)
                    self.frame_queues.append(queue.Queue(maxsize=10))
                    self.camera_name.append(cam_name)
                    logger.info(f"Camera {cam_name} initialized successfully")
                else:
                    logger.error(f"Failed to open camera: {cam_name}")
            except Exception as e:
                logger.error(f"Error initializing camera {cam_name}: {e}")
    
    def stop_connections(self):
        """Clean up camera connections"""
        for cap in self.cap_devices:
            if cap and cap.isOpened():
                cap.release()
        self.cap_devices = []
        self.frame_queues = []
        self.helmet_frames = []
        self.camera_name = []
        self.warning_count = []
    
    def stop(self):
        """Stop helmet detection system"""
        if not self.running:
            return
            
        self.running = False
        time.sleep(0.5)  # Allow threads to exit gracefully
        self.stop_connections()
        logger.info("Helmet detection stopped")
    
    def read_frames(self, idx: int, cap: cv2.VideoCapture):
        """Read frames from camera"""
        reconnect_attempts = 0
        max_reconnect_attempts = 5
        
        while self.running:
            try:
                ret, frame = cap.read()
                if not ret:
                    reconnect_attempts += 1
                    if reconnect_attempts > max_reconnect_attempts:
                        break
                    time.sleep(1)
                    continue
                    
                reconnect_attempts = 0
                
                # Resize frame for consistency
                frame = cv2.resize(frame, (1080, 780))
                
                with self.lock:
                    if idx < len(self.frame_queues):
                        if self.frame_queues[idx].full():
                            self.frame_queues[idx].get()  # Remove oldest frame
                        self.frame_queues[idx].put(frame)
                    else:
                        break
                        
            except Exception as e:
                logger.error(f"Error reading frames from camera {idx}: {e}")
                time.sleep(0.1)
    
    def process_frames(self, idx: int):
        """Process frames for helmet detection"""
        while self.running:
            try:
                if idx >= len(self.frame_queues):
                    break
                    
                if self.frame_queues[idx].empty():
                    time.sleep(0.01)
                    continue
                    
                frame = self.frame_queues[idx].get()
                
                # Perform helmet detection
                processed_frame, detections = self.detect_helmets(frame, idx)
                
                # Update frame for display
                with self.lock:
                    if idx < len(self.helmet_frames):
                        # Encode frame for transmission
                        _, buffer = cv2.imencode('.jpg', processed_frame)
                        self.helmet_frames[idx] = buffer.tobytes()
                
                # Save detections to database
                if detections:
                    self.save_detections(detections, idx)
                
            except Exception as e:
                logger.error(f"Error processing frames for camera {idx}: {e}")
                time.sleep(0.1)
    
    def detect_helmets(self, frame: np.ndarray, camera_idx: int):
        """Detect helmets in frame"""
        try:
            # Run object detection
            results = self.object_model(frame, conf=self.conf_threshold)
            
            detections = []
            person_count = 0
            no_helmet_count = 0
            
            # Process detections
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get detection info
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = box.conf[0].cpu().numpy()
                        cls = int(box.cls[0].cpu().numpy())
                        
                        # Map class to detection type
                        class_names = ['person', 'helmet', 'no_helmet']  # Adjust based on your model
                        if cls < len(class_names):
                            detection_type = class_names[cls]
                        else:
                            continue
                            
                        # Count detections
                        if detection_type == 'person':
                            person_count += 1
                        elif detection_type == 'no_helmet':
                            no_helmet_count += 1
                            
                        # Draw bounding box
                        color = (0, 255, 0) if detection_type == 'helmet' else (0, 0, 255)
                        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                        cv2.putText(frame, f"{detection_type}: {conf:.2f}", 
                                  (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                        
                        # Store detection
                        detections.append({
                            'detection_type': detection_type,
                            'confidence': float(conf),
                            'bbox_x': int(x1),
                            'bbox_y': int(y1),
                            'bbox_width': int(x2 - x1),
                            'bbox_height': int(y2 - y1)
                        })
            
            # Update warning counts
            if camera_idx < len(self.warning_count):
                self.warning_count[camera_idx].update({
                    'person_count': person_count,
                    'no_helmet_count': no_helmet_count,
                    'no_vest_count': 0,  # Placeholder
                    'no_gloves_count': 0  # Placeholder
                })
            
            # Generate alerts for safety violations
            if no_helmet_count > 0:
                self.generate_alert(camera_idx, no_helmet_count)
            
            return frame, detections
            
        except Exception as e:
            logger.error(f"Error in helmet detection: {e}")
            return frame, []
    
    def save_detections(self, detections: List[Dict], camera_idx: int):
        """Save detections to database"""
        try:
            db = SessionLocal()
            camera_name = self.camera_name[camera_idx] if camera_idx < len(self.camera_name) else f"Camera {camera_idx}"
            
            for detection in detections:
                db_detection = Detection(
                    camera_name=camera_name,
                    detection_type=detection['detection_type'],
                    confidence=detection['confidence'],
                    bbox_x=detection['bbox_x'],
                    bbox_y=detection['bbox_y'],
                    bbox_width=detection['bbox_width'],
                    bbox_height=detection['bbox_height'],
                    timestamp=datetime.now(timezone.utc)
                )
                db.add(db_detection)
            
            db.commit()
            db.close()
            
        except Exception as e:
            logger.error(f"Error saving detections: {e}")
    
    def generate_alert(self, camera_idx: int, violation_count: int):
        """Generate safety alert"""
        try:
            camera_name = self.camera_name[camera_idx] if camera_idx < len(self.camera_name) else f"Camera {camera_idx}"
            current_time = time.time()
            
            # Check cooldown to prevent spam
            if camera_name in self.alert_cooldown:
                if current_time - self.alert_cooldown[camera_name] < self.cooldown_duration:
                    return
            
            self.alert_cooldown[camera_name] = current_time
            
            # Save alert to database
            db = SessionLocal()
            alert = Alert(
                camera_name=camera_name,
                alert_type="no_helmet_detected",
                message=f"Safety violation detected: {violation_count} person(s) without helmet",
                timestamp=datetime.now(timezone.utc)
            )
            db.add(alert)
            db.commit()
            db.close()
            
            logger.warning(f"Alert generated for {camera_name}: {violation_count} helmet violations")
            
        except Exception as e:
            logger.error(f"Error generating alert: {e}")
