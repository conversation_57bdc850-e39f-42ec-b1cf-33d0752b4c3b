from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, status
from fastapi.responses import HTMLR<PERSON>ponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import requests

# Import dashboard modules
from .config import settings
from .middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Dashboard Service", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.middleware("http")(auth_middleware)

# Setup templates and static files
templates = Jinja2Templates(directory="dashboard_service/templates")
app.mount("/static", StaticFiles(directory="dashboard_service/static"), name="static")

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "dashboard"}

# Main dashboard
@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

# Service proxy endpoints - redirect to respective services
@app.get("/face-recognition")
async def face_recognition_redirect():
    return RedirectResponse(url="http://localhost:8001/", status_code=status.HTTP_302_FOUND)

@app.get("/crowd-detection")
async def crowd_detection_redirect():
    return RedirectResponse(url="http://localhost:8002/", status_code=status.HTTP_302_FOUND)

@app.get("/helmet-detection")
async def helmet_detection_redirect():
    return RedirectResponse(url="http://localhost:8003/", status_code=status.HTTP_302_FOUND)

@app.get("/quality-control")
async def quality_control_redirect():
    return RedirectResponse(url="http://localhost:8004/", status_code=status.HTTP_302_FOUND)

# Logout endpoint
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="http://localhost:8000/logout", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
