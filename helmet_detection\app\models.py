from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, JSON, Boolean, Float, Text
from database import Base, AuthBase
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import UploadFile


# Helmet Detection Models
class Detection(Base):
    __tablename__ = 'detections'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    camera_name = Column(String(100), nullable=True)
    image_path = Column(String(255), nullable=True)
    detection_type = Column(String(50))  # 'helmet', 'no_helmet', 'person'
    confidence = Column(Float)
    bbox_x = Column(Integer)
    bbox_y = Column(Integer)
    bbox_width = Column(Integer)
    bbox_height = Column(Integer)
    person_id = Column(String(100), nullable=True)  # For tracking

    def __repr__(self):
        return f"<Detection(id={self.id}, type={self.detection_type}, confidence={self.confidence})>"


class Alert(Base):
    __tablename__ = 'alerts'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    camera_name = Column(String(100), nullable=True)
    alert_type = Column(String(50))  # 'no_helmet_detected'
    message = Column(Text)
    image_path = Column(String(255), nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolved_at = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<Alert(id={self.id}, type={self.alert_type}, resolved={self.is_resolved})>"


class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Camera(id={self.id}, name={self.name}, active={self.is_active})>"


class Settings(Base):
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True)
    value = Column(String(255))
    description = Column(Text, nullable=True)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Settings(key={self.key}, value={self.value})>"


# Auth models for authentication
class AuthUser(AuthBase):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="user")
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# OTP model for verification
class OTP(AuthBase):
    __tablename__ = "otps"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), index=True)
    code = Column(String(6))
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    is_used = Column(Boolean, default=False)

    def is_valid(self):
        """Check if OTP is valid (not expired and not used)"""
        now = datetime.utcnow()
        is_not_used = not self.is_used
        is_not_expired = now < self.expires_at
        return is_not_used and is_not_expired


# Pydantic models for API requests/responses
class DetectionCreate(BaseModel):
    camera_name: str
    detection_type: str
    confidence: float
    bbox_x: int
    bbox_y: int
    bbox_width: int
    bbox_height: int
    person_id: Optional[str] = None
    image_path: Optional[str] = None


class DetectionResponse(BaseModel):
    id: int
    timestamp: datetime
    camera_name: Optional[str]
    detection_type: str
    confidence: float
    bbox_x: int
    bbox_y: int
    bbox_width: int
    bbox_height: int
    person_id: Optional[str]
    image_path: Optional[str]

    class Config:
        orm_mode = True


class AlertCreate(BaseModel):
    camera_name: str
    alert_type: str
    message: str
    image_path: Optional[str] = None


class AlertResponse(BaseModel):
    id: int
    timestamp: datetime
    camera_name: Optional[str]
    alert_type: str
    message: str
    image_path: Optional[str]
    is_resolved: bool
    resolved_at: Optional[datetime]

    class Config:
        orm_mode = True


class CameraCreate(BaseModel):
    name: str
    rtsp_url: str


class CameraResponse(BaseModel):
    id: int
    name: str
    rtsp_url: str
    is_active: bool
    created_at: datetime

    class Config:
        orm_mode = True


class SettingsCreate(BaseModel):
    key: str
    value: str
    description: Optional[str] = None


class SettingsResponse(BaseModel):
    id: int
    key: str
    value: str
    description: Optional[str]
    updated_at: datetime

    class Config:
        orm_mode = True


# Token models for authentication
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# OTP Pydantic models
class OTPCreate(BaseModel):
    email: EmailStr

class OTPVerify(BaseModel):
    email: EmailStr
    code: str
