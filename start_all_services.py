#!/usr/bin/env python3
"""
Master startup script for all VigilanteEye microservices
This script starts all services in the correct order
"""

import subprocess
import time
import sys
import os
from concurrent.futures import ThreadPoolExecutor

def start_service(script_name, service_name):
    """Start a service using its startup script"""
    print(f"Starting {service_name}...")
    try:
        process = subprocess.Popen([sys.executable, script_name])
        return process
    except Exception as e:
        print(f"Error starting {service_name}: {e}")
        return None

def main():
    print("=" * 60)
    print("VigilanteEye Microservices Startup")
    print("=" * 60)
    print("Starting all services in the correct order...")
    print()
    
    services = [
        ("start_auth_service.py", "Auth Service (Port 8000)"),
        ("start_face_recognition_service.py", "Face Recognition Service (Port 8001)"),
        ("start_crowd_detection_service.py", "Crowd Detection Service (Port 8002)"),
        ("start_helmet_detection_service.py", "Helmet Detection Service (Port 8003)"),
        ("start_quality_control_service.py", "Quality Control Service (Port 8004)"),
        ("start_dashboard_service.py", "Dashboard Service (Port 8005)"),
    ]
    
    processes = []
    
    # Start Auth Service first and wait a bit
    auth_process = start_service(services[0][0], services[0][1])
    if auth_process:
        processes.append(auth_process)
        print("Waiting for Auth Service to initialize...")
        time.sleep(3)
    
    # Start other services
    for script, name in services[1:]:
        process = start_service(script, name)
        if process:
            processes.append(process)
        time.sleep(1)  # Small delay between service starts
    
    print()
    print("=" * 60)
    print("All services started!")
    print("=" * 60)
    print("Service URLs:")
    print("- Auth Service: http://localhost:8000/login")
    print("- Face Recognition: http://localhost:8001")
    print("- Crowd Detection: http://localhost:8002")
    print("- Helmet Detection: http://localhost:8003")
    print("- Quality Control: http://localhost:8004")
    print("- Main Dashboard: http://localhost:8005")
    print("=" * 60)
    print("Press Ctrl+C to stop all services")
    
    try:
        # Wait for all processes
        for process in processes:
            process.wait()
    except KeyboardInterrupt:
        print("\nShutting down all services...")
        for process in processes:
            process.terminate()
        print("All services stopped.")

if __name__ == "__main__":
    main()
