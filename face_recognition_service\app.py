from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import requests

# Import face recognition modules
from .database import get_db, create_tables
from .config import settings
from .routes import router as face_recognition_router
from .middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Face Recognition Service", version="1.0.0")

# Create database tables
create_tables()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.middleware("http")(auth_middleware)

# Setup templates and static files
templates = Jinja2Templates(directory="face_recognition_service/templates")
app.mount("/static", StaticFiles(directory="face_recognition_service/static"), name="static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")

# Mount cropped faces and images
import os
os.makedirs("static/images", exist_ok=True)
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include face recognition routes
app.include_router(face_recognition_router, prefix="", tags=["Face Recognition"])

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "face_recognition"}

# Root endpoint
@app.get("/", response_class=HTMLResponse)
async def face_recognition_home(request: Request):
    return templates.TemplateResponse("face_recognition.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
