#!/usr/bin/env python3
"""
Startup script for Face Recognition Service
Run this to start the face recognition service on port 8001
"""

import uvicorn
import sys
import os

# Add the face_recognition_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'face_recognition_service'))

if __name__ == "__main__":
    print("Starting Face Recognition Service on http://localhost:8001")
    print("This service handles face recognition and attendance tracking")
    print("Access service at: http://localhost:8001")
    print("=" * 50)
    
    uvicorn.run(
        "face_recognition_service.app:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
