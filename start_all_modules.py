#!/usr/bin/env python3
"""
Master startup script for all VigilanteEye modules
This script starts all modules in the correct order
"""

import subprocess
import time
import sys

def start_service(script_name, service_name):
    """Start a service using its startup script"""
    print(f"Starting {service_name}...")
    try:
        process = subprocess.Popen([sys.executable, script_name])
        return process
    except Exception as e:
        print(f"Error starting {service_name}: {e}")
        return None

def main():
    print("=" * 60)
    print("VigilanteEye Modules Startup")
    print("=" * 60)
    print("Starting all modules in the correct order...")
    print()
    
    services = [
        ("start_authentication.py", "Authentication Module (Port 8000)"),
        ("start_face_recognition.py", "Face Recognition Module (Port 8001)"),
        ("start_crowd_detection.py", "Crowd Detection Module (Port 8002)"),
        ("start_helmet_detection.py", "Helmet Detection Module (Port 8003)"),
        ("start_quality_control.py", "Quality Control Module (Port 8004)"),
    ]
    
    processes = []
    
    # Start authentication first and wait a bit
    auth_process = start_service(services[0][0], services[0][1])
    if auth_process:
        processes.append(auth_process)
        print("Waiting for Authentication Module to initialize...")
        time.sleep(3)
    
    # Start other modules
    for script, name in services[1:]:
        process = start_service(script, name)
        if process:
            processes.append(process)
        time.sleep(2)  # Small delay between module starts

    print()
    print("=" * 60)
    print("All modules started!")
    print("=" * 60)
    print("Module URLs:")
    print("- Authentication: http://localhost:8000")
    print("- Face Recognition: http://localhost:8001")
    print("- Crowd Detection: http://localhost:8002")
    print("- Helmet Detection: http://localhost:8003")
    print("- Quality Control: http://localhost:8004")
    print("=" * 60)
    print("Press Ctrl+C to stop all modules")
    
    try:
        # Wait for all processes
        for process in processes:
            process.wait()
    except KeyboardInterrupt:
        print("\nShutting down all modules...")
        for process in processes:
            process.terminate()
        print("All modules stopped.")

if __name__ == "__main__":
    main()
