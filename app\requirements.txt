absl-py==2.1.0
annotated-types==0.7.0
anyio==4.8.0
asttokens==3.0.0
astunparse==1.6.3
boto3==1.37.7
botocore==1.37.7
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.1
cryptography==44.0.2
cssselect2==0.8.0
cycler==0.12.1
debugpy==1.8.12
decorator==5.2.1
Deprecated==1.2.18
dnspython==2.7.0
email_validator==2.2.0
exceptiongroup==1.2.2
executing==2.2.0
fastapi==0.115.11
fastapi-cli==0.0.7
filelock==3.17.0
flatbuffers==25.2.10
fonttools==4.56.0
fsspec==2025.2.0
gast==0.4.0
google-auth==2.38.0
google-auth-oauthlib==0.4.6
google-pasta==0.2.0
grpcio==1.71.0
h11==0.14.0
h5py==3.13.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
humanfriendly==10.0
hydra-core==1.3.2
idna==3.10
imagededup==0.3.2
ipykernel==6.29.5
ipython==8.33.0
jedi==0.19.2
Jinja2==3.1.6
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
keras==2.10.0
Keras-Preprocessing==1.1.2
kiwisolver==1.4.8
libclang==18.1.1
Mako==1.3.9
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mpmath==1.3.0
nest-asyncio==1.6.0
networkx==3.4.2
numpy==1.26.4
nvidia-pyindex==1.0.9
oauthlib==3.2.2
onnx==1.17.0
onnxruntime==1.20.1
onnxruntime-gpu==1.20.1
onnxsim==0.4.36
onnxslim==0.1.48
opencv-python==*********
opt_einsum==3.4.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pillow==11.1.0
pip-tools==7.4.1
platformdirs==4.3.6
prompt_toolkit==3.0.50
protobuf==3.19.6
psutil==7.0.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycuda==2025.1
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
pyHanko==0.25.3
pyhanko-certvalidator==0.26.5
pyparsing==3.2.1
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytools==2025.1.1
pytz==2025.1
pywin32==308
PyYAML==6.0.2
pyzmq==26.2.1
requests==2.32.3
requests-oauthlib==2.0.0
rich==13.9.4
rich-toolkit==0.13.2
rsa==4.9
s3transfer==0.11.4
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
sphinx-rtd-theme==1.3.0
sphinxcontrib-jquery==4.1
stack-data==0.6.3
starlette==0.46.0
svglib==1.5.1
sympy==1.13.1
tensorboard==2.10.1
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorflow==2.10.1
tensorflow-estimator==2.10.0
tensorflow-io-gcs-filesystem==0.31.0
# tensorrt @ file:///C:/Users/<USER>/Documents/TensorRT-*********/python/tensorrt-*********-cp310-none-win_amd64.whl#sha256=aad107c082a2749c57fa5ed9ef0df8cd79a8278dcaa3a29fe0337b9ba21fc460
# tensorrt_dispatch @ file:///C:/Users/<USER>/Documents/TensorRT-*********/python/tensorrt_dispatch-*********-cp310-none-win_amd64.whl#sha256=be2f74225834ee9242630c4ba27713140649c891b704acdf47cf95dc2f9f5b74
# tensorrt_lean @ file:///C:/Users/<USER>/Documents/TensorRT-*********/python/tensorrt_lean-*********-cp310-none-win_amd64.whl#sha256=6c1ee23dd0e57f291078993437175dadbbaf955a2c78d1087c26afc5c79429f9
termcolor==2.5.0
threadpoolctl==3.6.0
torch==2.6.0+cu126
torchaudio==2.6.0+cu126
torchvision==0.21.0+cu126
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
typer==0.15.2
typing_extensions==4.12.2
tzdata==2025.1
ultralytics==8.3.84
ultralytics-thop==2.0.14
urllib3==2.3.0
uvicorn==0.34.0
watchfiles==1.0.4
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
xhtml2pdf==0.2.11

absl-py==2.1.0
alembic==1.14.0
annotated-types==0.7.0
anyio==4.7.0
astunparse==1.6.3
bcrypt==4.2.1
beautifulsoup4==4.12.3
blinker==1.9.0
certifi==2024.12.14
charset-normalizer==3.4.1
click==8.1.8
cmake==3.30.5
colorama==0.4.6
contourpy==1.3.1
cryptography
cycler==0.12.1
databases==0.9.0
deepface==0.0.93
deep-sort-realtime
keras-facenet
face_detection==0.2.2
fastapi==0.115.6
filelock==3.16.1
fire==0.7.0
Flask==3.1.0
Flask-Cors==5.0.0
flatbuffers==24.12.23
fonttools==4.55.3
fsspec==2024.12.0
gast==0.6.0
gdown==5.2.0
google-pasta==0.2.0
greenlet==3.1.1
grpcio==1.68.1
gunicorn==23.0.0
h11==0.14.0
h5py==3.12.1
huggingface-hub
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.5
joblib==1.4.2
keras==3.7.0
kiwisolver==1.4.8
libclang==18.1.1
lz4==4.3.3
Mako==1.3.8
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.0
mdurl==0.1.2
ml-dtypes==0.4.1
mpmath==1.3.0
mtcnn==1.0.0
namex==0.0.8
networkx==3.4.2
numpy==2.0.2
opencv-python==*********
opt_einsum==3.4.0
optree==0.13.1
packaging==24.2
pandas==2.2.3
pillow==11.1.0
protobuf==5.29.2
pydantic==2.10.4
pydantic-settings==2.7.0
pydantic_core==2.27.2
Pygments==2.18.0
PyMySQL==1.1.1
pyparsing==3.2.1
PySide6==6.8.1
PySide6_Addons==6.8.1
PySide6_Essentials==6.8.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2024.2
requests==2.32.3
retina-face==0.0.17
rich==13.9.4
scikit-learn==1.6.0
scipy==1.14.1
setuptools==75.6.0
shiboken6==6.8.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.36
starlette==0.41.3
sympy==1.13.1
tensorboard==2.18.0
tensorboard-data-server==0.7.2
tensorflow==2.18.0
tensorflow_intel==2.18.0
termcolor==2.5.0
tf_keras==2.18.0
threadpoolctl==3.5.0
torch==2.5.1
torchvision==0.20.1
tqdm==4.67.1
typing_extensions==4.12.2
tzdata==2024.2
urllib3==2.3.0
uvicorn==0.34.0
ultralytics
Werkzeug==3.1.3
wheel==0.45.1
wrapt==1.17.0

