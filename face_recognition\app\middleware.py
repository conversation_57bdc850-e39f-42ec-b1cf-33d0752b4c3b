from fastapi import Request, status
from fastapi.responses import RedirectResponse
from auth_client import auth_client

async def auth_middleware(request: Request, call_next):
    """Middleware to verify authentication via auth service"""

    # Skip authentication for login page, static files, and API endpoints
    if (request.url.path.startswith("/login") or
        request.url.path.startswith("/static") or
        request.url.path.startswith("/Dataset") or
        request.url.path.startswith("/images") or
        request.url.path == "/token"):
        response = await call_next(request)
        return response

    # Get token from cookie
    token = request.cookies.get("access_token")

    if not token:
        # Redirect to login page
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    # Remove "Bearer " prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        # Verify token with authentication service
        user_info = auth_client.verify_token(token)
        if user_info and user_info.get("valid"):
            # Add user info to request state
            request.state.user = {
                "username": user_info.get("username"),
                "role": user_info.get("role", "user")
            }
            response = await call_next(request)
            return response
    except Exception as e:
        print(f"Auth middleware error: {e}")

    # Token invalid or user not found
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
