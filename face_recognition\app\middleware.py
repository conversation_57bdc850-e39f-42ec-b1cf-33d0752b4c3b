from fastapi import Request

async def auth_middleware(request: Request, call_next):
    """Middleware - No authentication required, direct access"""

    # Set default user info for compatibility
    request.state.user = {
        "username": "anonymous",
        "role": "user"
    }

    # Allow all requests to proceed without authentication
    response = await call_next(request)
    return response
