#!/usr/bin/env python3
"""
Startup script for Face Recognition Module
Run this to start the face recognition module on port 8001
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Face Recognition Module on http://localhost:8001")
    print("This module handles face recognition and attendance tracking")
    print("Access module at: http://localhost:8001")
    print("Login at: http://localhost:8001/login")
    print("=" * 50)
    
    uvicorn.run(
        "face_recognition.app.main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
