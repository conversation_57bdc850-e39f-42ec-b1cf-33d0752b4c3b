<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users</title>
    <style>
        /* Basic Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Body Styling */
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            color: #333;
            padding: 20px;
        }

        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        /* Table Styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 12px 20px;
            text-align: left;
            font-size: 16px;
        }

        th {
            background-color: #2980b9;
            color: white;
        }

        td {
            background-color: #ecf0f1;
            border-bottom: 1px solid #ddd;
        }

        tr:nth-child(even) td {
            background-color: #f9f9f9;
        }

        /* Button Styling */
        button {
            background-color: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #c0392b;
        }

        button:focus {
            outline: none;
        }

        /* Go to Home Button Styling */
        #home-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
        }

        #home-btn:hover {
            background-color: #45a049;
        }

        /* Responsive Styling */
        @media screen and (max-width: 768px) {
            th, td {
                padding: 10px;
            }

            button {
                font-size: 12px;
            }

            #home-btn {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>

    <!-- Button to go to Home Page -->
    <button id="home-btn" type="button">Go Dashboard</button>

    <h2>Users Management</h2>

    <!-- Table to Display Users -->
    <table id="users-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <!-- Users will be dynamically inserted here -->
        </tbody>
    </table>

    <script>
        // Function to fetch users and display in the table
        async function fetchUsers() {
            const response = await fetch('http://127.0.0.1:8000/face_recognition/users');
            const users = await response.json();

            const tableBody = document.querySelector('#users-table tbody');
            tableBody.innerHTML = ''; // Clear the table before adding new rows

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = ` 
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><button onclick="deleteUser(${user.id})">Delete</button></td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Function to delete a user by their ID
        async function deleteUser(userId) {
            const response = await fetch(`http://127.0.0.1:8000/face_recognition/users/${userId}`, {
                method: 'DELETE',
            });

            const result = await response.json();

            if (response.ok) {
                alert(result.message); // Show success message
                fetchUsers(); // Refresh the user list
            } else {
                alert('Error: ' + result.detail); // Show error message
            }
        }

        const homeBtn = document.getElementById("home-btn");
        homeBtn.addEventListener("click", () => {
            // Redirect to the homepage
            window.location.href = "/face_recognition/face_recognition";
        });

        // Fetch users when the page loads
        window.onload = fetchUsers;

    </script>
</body>
</html>
