<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Face Recognition System</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .header h1 {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.2rem;
      opacity: 0.8;
    }

    .service-info {
      background: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 15px;
      margin: 30px 0;
      text-align: center;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 30px 0;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }

    .btn-primary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-primary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-danger {
      background: #e74c3c;
      color: white;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #e74c3c;
    }

    .status-dot.active {
      background: #27ae60;
    }

    .video-section {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 15px;
      margin: 20px 0;
      text-align: center;
    }

    .logout-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  </style>
</head>
<body>
  <a href="/logout" class="logout-btn">Logout</a>
  
  <div class="container">
    <div class="header">
      <h1>Face Recognition System</h1>
      <p>Standalone Service - Port 8001</p>
    </div>

    <div class="service-info">
      <h2>Face Recognition & Attendance Tracking</h2>
      <p>This is an independent FastAPI application for face recognition and attendance management.</p>
      <p>Features include real-time face detection, user registration, and attendance logging.</p>
    </div>

    <div class="status-indicator">
      <div class="status-dot" id="statusDot"></div>
      <span id="statusText">System Inactive</span>
    </div>

    <div class="controls">
      <button class="btn btn-success" id="startBtn">Start Monitoring</button>
      <button class="btn btn-danger" id="stopBtn">Stop Monitoring</button>
      <a href="/registration" class="btn btn-primary">Register User</a>
      <a href="/attendance" class="btn btn-primary">View Attendance</a>
      {% if user_role == "admin" %}
      <a href="/admin" class="btn btn-primary">Admin Panel</a>
      {% endif %}
    </div>

    <div class="video-section" id="videoSection">
      <h3>Camera Feeds</h3>
      <div id="feeds">
        <p>Click "Start Monitoring" to begin face recognition</p>
      </div>
    </div>
  </div>

  <script>
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const feedsDiv = document.getElementById('feeds');

    let isStreaming = false;

    function updateStatus(active) {
      if (active) {
        statusDot.classList.add('active');
        statusText.textContent = 'System Active';
      } else {
        statusDot.classList.remove('active');
        statusText.textContent = 'System Inactive';
      }
    }

    startBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/start', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
          isStreaming = true;
          updateStatus(true);
          feedsDiv.innerHTML = '<p>Monitoring system started successfully</p>';
          alert('Face recognition started successfully!');
        } else {
          alert('Failed to start: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });

    stopBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/stop', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
          isStreaming = false;
          updateStatus(false);
          feedsDiv.innerHTML = '<p>Click "Start Monitoring" to begin face recognition</p>';
          alert('Face recognition stopped successfully!');
        } else {
          alert('Failed to stop: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });
  </script>
</body>
</html>
