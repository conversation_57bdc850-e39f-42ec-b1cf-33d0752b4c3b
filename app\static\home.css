/* Reset styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9fb;
    color: #333;
}

/* Header styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    height: 50px;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    letter-spacing: 1px;
}

/* Main content styles */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 120px; /* To account for the fixed header */
    padding: 20px;
}

/* Clickable container style */
.box {
    width: 280px;
    height: 180px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    color: white;
    font-size: 1.4rem;
    font-weight: bold;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.box:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

/* Background images with gradients */
.box.face {
    background: linear-gradient(135deg, #11b73a 0%, #ff5219 100%), 
                url('https://via.placeholder.com/280x180?text=Face+Recognition') center/cover no-repeat;
}

.box.helmet {
    background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%), 
                url('https://via.placeholder.com/280x180?text=Helmet+Detection') center/cover no-repeat;
}

.box.crowd {
    background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%), 
                url('https://via.placeholder.com/280x180?text=Crowd+Detection') center/cover no-repeat;
}

.box.quality {
    background: linear-gradient(135deg, #1345b8 0%, #d45b5b 100%),
                url('https://via.placeholder.com/280x180?text=Quality+Control') center/cover no-repeat;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
    }

    .box {
        width: 100%;
        max-width: 280px;
    }

    .logo-container {
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }
}

/* Logout button styles */
.logout-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px 15px;
    background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 126, 95, 0.2);
}