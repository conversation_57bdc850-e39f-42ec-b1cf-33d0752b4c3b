-- Create databases for each microservice
CREATE DATABASE IF NOT EXISTS vigilanteye;
CREATE DATABASE IF NOT EXISTS face_recognition_db;
CREATE DATABASE IF NOT EXISTS crowd_detection_db;
CREATE DATABASE IF NOT EXISTS helmet_detection_db;
CREATE DATABASE IF NOT EXISTS quality_control_db;

-- Grant permissions
GRANT ALL PRIVILEGES ON vigilanteye.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON face_recognition_db.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON crowd_detection_db.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON helmet_detection_db.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON quality_control_db.* TO 'root'@'%';

FLUSH PRIVILEGES;
