#!/usr/bin/env python3
"""
Startup script for Crowd Detection Service
Run this to start the crowd detection service on port 8002
"""

import uvicorn
import sys
import os

# Add the crowd_detection_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'crowd_detection_service'))

if __name__ == "__main__":
    print("Starting Crowd Detection Service on http://localhost:8002")
    print("This service handles crowd detection and monitoring")
    print("Access service at: http://localhost:8002")
    print("=" * 50)
    
    uvicorn.run(
        "crowd_detection_service.app:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
