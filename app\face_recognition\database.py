from sqlalchemy import create_engine
from app.face_recognition.config import settings
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Create declarative bases for both databases
Base = declarative_base()  # For surveillance_system
AuthBase = declarative_base()  # For vigilanteye auth database

# Main database URL (surveillance_system)
SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
)


# Create engines
engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_size=10, max_overflow=20)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Function to create all tables in main database
def create_tables():
    Base.metadata.create_all(bind=engine)


# Main database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Auth database dependency
def get_auth_db():
    db = AuthSessionLocal()
    try:
        yield db
    finally:
        db.close()
