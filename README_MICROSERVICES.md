# VigilanteEye Microservices Architecture

This document explains the new microservices architecture for the VigilanteEye surveillance system.

## Architecture Overview

The application has been restructured from a monolithic FastAPI application to a microservices architecture with the following services:

### Services and Ports

1. **Auth Service** (Port 8000)
   - Handles user authentication and authorization
   - Login, registration, OTP verification
   - Token generation and verification for other services

2. **Face Recognition Service** (Port 8001)
   - Face recognition and attendance tracking
   - User management and encoding generation
   - Camera management and permissions

3. **Crowd Detection Service** (Port 8002)
   - Crowd detection and monitoring
   - Alert system for crowd density
   - ROI (Region of Interest) management

4. **Helmet Detection Service** (Port 8003)
   - Helmet detection and safety monitoring
   - PPE compliance tracking
   - Safety alert system

5. **Quality Control Service** (Port 8004)
   - Quality control and inspection
   - Product quality assessment
   - Defect detection

6. **Dashboard Service** (Port 8005)
   - Main dashboard and service gateway
   - Routes to individual services
   - Service health monitoring

## Database Architecture

Each service has its own dedicated database:

- `vigilanteye` - Auth Service database
- `face_recognition_db` - Face Recognition Service database
- `crowd_detection_db` - Crowd Detection Service database
- `helmet_detection_db` - Helmet Detection Service database
- `quality_control_db` - Quality Control Service database

## Running the Services

### Option 1: Start All Services at Once

```bash
python start_all_services.py
```

This will start all services in the correct order with proper delays.

### Option 2: Start Services Individually

Start each service in separate terminal windows:

```bash
# Terminal 1 - Auth Service
python start_auth_service.py

# Terminal 2 - Face Recognition Service
python start_face_recognition_service.py

# Terminal 3 - Crowd Detection Service
python start_crowd_detection_service.py

# Terminal 4 - Helmet Detection Service
python start_helmet_detection_service.py

# Terminal 5 - Quality Control Service
python start_quality_control_service.py

# Terminal 6 - Dashboard Service
python start_dashboard_service.py
```

### Option 3: Using Docker Compose

```bash
docker-compose -f docker-compose.microservices.yaml up
```

## Access Points

- **Main Dashboard**: http://localhost:8005
- **Auth Service**: http://localhost:8000/login
- **Face Recognition**: http://localhost:8001
- **Crowd Detection**: http://localhost:8002
- **Helmet Detection**: http://localhost:8003
- **Quality Control**: http://localhost:8004

## Authentication Flow

1. User accesses the Dashboard Service (Port 8005)
2. Dashboard redirects to Auth Service (Port 8000) for login
3. After successful login, Auth Service redirects back to Dashboard
4. Dashboard provides links to individual services
5. Each service verifies authentication with Auth Service

## Service Communication

- All services communicate with the Auth Service for token verification
- Services are stateless and can be scaled independently
- Each service has its own database and configuration
- Cross-service communication happens via HTTP APIs

## Development

### Adding New Features

1. Identify which service the feature belongs to
2. Add routes and logic to the appropriate service
3. Update the service's models and database if needed
4. Test the service independently

### Service Dependencies

- All services depend on the Auth Service for authentication
- Services can be developed and deployed independently
- Database migrations are handled per service

## Benefits of Microservices Architecture

1. **Scalability**: Each service can be scaled independently
2. **Maintainability**: Smaller, focused codebases
3. **Technology Diversity**: Each service can use different technologies
4. **Fault Isolation**: Failure in one service doesn't affect others
5. **Team Independence**: Different teams can work on different services
6. **Deployment Flexibility**: Services can be deployed independently

## Migration from Monolithic

The original monolithic application (`app/main.py`) has been split into:

- Authentication logic → Auth Service
- Face recognition module → Face Recognition Service
- Crowd detection module → Crowd Detection Service
- Helmet detection module → Helmet Detection Service
- Quality control module → Quality Control Service
- Main dashboard → Dashboard Service

Each service maintains its own:
- Database configuration
- Models and schemas
- Routes and business logic
- Templates and static files
- Authentication middleware

## Troubleshooting

### Service Not Starting
- Check if the port is already in use
- Verify database connection settings
- Check Python path and dependencies

### Authentication Issues
- Ensure Auth Service is running first
- Check token expiration settings
- Verify service URLs in configuration

### Database Issues
- Run the database initialization script
- Check database credentials
- Ensure all required databases exist

## Future Enhancements

1. **API Gateway**: Add a proper API gateway for routing
2. **Service Discovery**: Implement service discovery mechanism
3. **Load Balancing**: Add load balancers for high availability
4. **Monitoring**: Add comprehensive monitoring and logging
5. **Message Queues**: Implement async communication between services
