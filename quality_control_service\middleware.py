from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse
import requests
from .config import settings

async def auth_middleware(request: Request, call_next):
    """Middleware to verify authentication with auth service"""
    
    # Skip authentication for health check and static files
    if (request.url.path.startswith("/health") or 
        request.url.path.startswith("/static")):
        response = await call_next(request)
        return response
    
    # Get token from cookie
    token = request.cookies.get("access_token")
    
    if not token:
        # Redirect to auth service login
        return RedirectResponse(url=f"{settings.AUTH_SERVICE_URL}/login", status_code=status.HTTP_302_FOUND)
    
    # Remove "Bearer " prefix if present
    if token.startswith("Bearer "):
        token = token[7:]
    
    try:
        # Verify token with auth service
        response = requests.post(
            f"{settings.AUTH_SERVICE_URL}/verify-token",
            json={"token": token},
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("valid"):
                # Add user info to request state
                request.state.user = result.get("user")
                response = await call_next(request)
                return response
    except requests.RequestException:
        pass
    
    # Token invalid or auth service unavailable
    return RedirectResponse(url=f"{settings.AUTH_SERVICE_URL}/login", status_code=status.HTTP_302_FOUND)
