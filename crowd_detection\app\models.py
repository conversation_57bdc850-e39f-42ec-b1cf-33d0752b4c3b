from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, DateTime, JSON, Boolean, Float, Text
from database import Base, AuthBase
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import UploadFile


# Crowd Detection Models
class CrowdEvent(Base):
    __tablename__ = 'crowd_events'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    camera_name = Column(String(100), nullable=True)
    image_path = Column(String(255), nullable=True)
    person_count = Column(Integer)
    crowd_density = Column(Float)  # People per square meter
    alert_level = Column(String(20))  # 'low', 'medium', 'high', 'critical'
    roi_coordinates = Column(JSON, nullable=True)  # Region of interest coordinates

    def __repr__(self):
        return f"<CrowdEvent(id={self.id}, count={self.person_count}, density={self.crowd_density})>"


class Alert(Base):
    __tablename__ = 'alerts'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    camera_name = Column(String(100), nullable=True)
    alert_type = Column(String(50))  # 'overcrowding', 'high_density', 'evacuation_needed'
    message = Column(Text)
    person_count = Column(Integer)
    crowd_density = Column(Float)
    image_path = Column(String(255), nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolved_at = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<Alert(id={self.id}, type={self.alert_type}, resolved={self.is_resolved})>"


class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), nullable=False)
    roi_coordinates = Column(JSON, nullable=True)  # Region of interest for crowd detection
    max_capacity = Column(Integer, default=100)  # Maximum safe capacity
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Camera(id={self.id}, name={self.name}, capacity={self.max_capacity})>"


class CrowdThreshold(Base):
    __tablename__ = 'crowd_thresholds'

    id = Column(Integer, primary_key=True, index=True)
    camera_id = Column(Integer, ForeignKey('cameras.id'))
    low_threshold = Column(Integer, default=20)
    medium_threshold = Column(Integer, default=50)
    high_threshold = Column(Integer, default=80)
    critical_threshold = Column(Integer, default=100)
    density_threshold = Column(Float, default=2.0)  # People per square meter
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    camera = relationship("Camera")

    def __repr__(self):
        return f"<CrowdThreshold(camera_id={self.camera_id}, critical={self.critical_threshold})>"


class Settings(Base):
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True)
    value = Column(String(255))
    description = Column(Text, nullable=True)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Settings(key={self.key}, value={self.value})>"


# Auth models for authentication
class AuthUser(AuthBase):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="user")
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# OTP model for verification
class OTP(AuthBase):
    __tablename__ = "otps"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), index=True)
    code = Column(String(6))
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    is_used = Column(Boolean, default=False)

    def is_valid(self):
        """Check if OTP is valid (not expired and not used)"""
        now = datetime.utcnow()
        is_not_used = not self.is_used
        is_not_expired = now < self.expires_at
        return is_not_used and is_not_expired


# Pydantic models for API requests/responses
class CrowdEventCreate(BaseModel):
    camera_name: str
    person_count: int
    crowd_density: float
    alert_level: str
    roi_coordinates: Optional[List[int]] = None
    image_path: Optional[str] = None


class CrowdEventResponse(BaseModel):
    id: int
    timestamp: datetime
    camera_name: Optional[str]
    person_count: int
    crowd_density: float
    alert_level: str
    roi_coordinates: Optional[List[int]]
    image_path: Optional[str]

    class Config:
        orm_mode = True


class AlertCreate(BaseModel):
    camera_name: str
    alert_type: str
    message: str
    person_count: int
    crowd_density: float
    image_path: Optional[str] = None


class AlertResponse(BaseModel):
    id: int
    timestamp: datetime
    camera_name: Optional[str]
    alert_type: str
    message: str
    person_count: int
    crowd_density: float
    image_path: Optional[str]
    is_resolved: bool
    resolved_at: Optional[datetime]

    class Config:
        orm_mode = True


class CameraCreate(BaseModel):
    name: str
    rtsp_url: str
    roi_coordinates: Optional[List[int]] = None
    max_capacity: Optional[int] = 100


class CameraResponse(BaseModel):
    id: int
    name: str
    rtsp_url: str
    roi_coordinates: Optional[List[int]]
    max_capacity: int
    is_active: bool
    created_at: datetime

    class Config:
        orm_mode = True


class CrowdThresholdCreate(BaseModel):
    camera_id: int
    low_threshold: int = 20
    medium_threshold: int = 50
    high_threshold: int = 80
    critical_threshold: int = 100
    density_threshold: float = 2.0


class CrowdThresholdResponse(BaseModel):
    id: int
    camera_id: int
    low_threshold: int
    medium_threshold: int
    high_threshold: int
    critical_threshold: int
    density_threshold: float
    updated_at: datetime

    class Config:
        orm_mode = True


# Token models for authentication
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# OTP Pydantic models
class OTPCreate(BaseModel):
    email: EmailStr

class OTPVerify(BaseModel):
    email: EmailStr
    code: str
