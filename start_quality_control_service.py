#!/usr/bin/env python3
"""
Startup script for Quality Control Service
Run this to start the quality control service on port 8004
"""

import uvicorn
import sys
import os

# Add the quality_control_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'quality_control_service'))

if __name__ == "__main__":
    print("Starting Quality Control Service on http://localhost:8004")
    print("This service handles quality control and inspection")
    print("Access service at: http://localhost:8004")
    print("=" * 50)
    
    uvicorn.run(
        "quality_control_service.app:app",
        host="0.0.0.0",
        port=8004,
        reload=True,
        log_level="info"
    )
