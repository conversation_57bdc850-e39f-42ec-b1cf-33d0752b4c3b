# Face Recognition Models

This directory contains the machine learning models used by the face recognition service.

## Required Files

1. **shape_predictor_68_face_landmarks.dat** - Dlib facial landmark predictor
   - Download from: http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2
   - Extract and place in this directory

2. **YOLOv8 Face Detection Model** - Will be downloaded automatically from Hugging Face
   - Repository: arnabdhar/YOLOv8-Face-Detection
   - File: model.pt

## Setup Instructions

1. Download the dlib landmark predictor:
   ```bash
   wget http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2
   bunzip2 shape_predictor_68_face_landmarks.dat.bz2
   ```

2. Place the extracted file in this directory

3. The YOLOv8 model will be downloaded automatically when the service starts

## Model Information

- **Dlib Landmark Predictor**: Used for face alignment and landmark detection
- **YOLOv8 Face Detection**: Used for real-time face detection and tracking
- **FaceNet (PyTorch)**: Used for face encoding and recognition
