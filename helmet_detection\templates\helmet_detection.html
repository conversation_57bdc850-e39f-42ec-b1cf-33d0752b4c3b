<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Helmet Detection System</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .header h1 {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.2rem;
      opacity: 0.8;
    }

    .service-info {
      background: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 15px;
      margin: 30px 0;
      text-align: center;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 30px 0;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-primary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-success:hover {
      background: #229954;
      transform: translateY(-2px);
    }

    .btn-danger {
      background: #8e44ad;
      color: white;
    }

    .btn-danger:hover {
      background: #7d3c98;
      transform: translateY(-2px);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #8e44ad;
      transition: background-color 0.3s ease;
    }

    .status-dot.active {
      background: #27ae60;
    }

    .video-section {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 15px;
      margin: 20px 0;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 10px;
      text-align: center;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .logout-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .alert-section {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 15px;
      margin: 20px 0;
    }

    .alert-item {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      border-radius: 8px;
      margin: 10px 0;
      border-left: 4px solid #f39c12;
    }
  </style>
</head>
<body>
  <a href="/logout" class="logout-btn">Logout</a>
  
  <div class="container">
    <div class="header">
      <h1>Helmet Detection System</h1>
      <p>Safety Monitoring Service - Port 8003</p>
    </div>

    <div class="service-info">
      <h2>Helmet Detection & Safety Monitoring</h2>
      <p>This is an independent FastAPI application for helmet detection and workplace safety monitoring.</p>
      <p>Features include real-time helmet detection, safety alerts, and compliance tracking.</p>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalDetections">0</div>
        <div class="stat-label">Total Detections</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="helmetDetections">0</div>
        <div class="stat-label">Helmet Detections</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="noHelmetDetections">0</div>
        <div class="stat-label">No Helmet Violations</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="activeAlerts">0</div>
        <div class="stat-label">Active Alerts</div>
      </div>
    </div>

    <div class="status-indicator">
      <div class="status-dot" id="statusDot"></div>
      <span id="statusText">System Inactive</span>
    </div>

    <div class="controls">
      <button class="btn btn-success" id="startBtn">Start Monitoring</button>
      <button class="btn btn-danger" id="stopBtn">Stop Monitoring</button>
      <a href="/cameras" class="btn btn-primary">Manage Cameras</a>
      <a href="/detections" class="btn btn-primary">View Detections</a>
      <a href="/alerts" class="btn btn-primary">View Alerts</a>
      {% if user_role == "admin" %}
      <a href="/admin" class="btn btn-primary">Admin Panel</a>
      {% endif %}
    </div>

    <div class="video-section" id="videoSection">
      <h3>Camera Feeds</h3>
      <div id="feeds">
        <p>Click "Start Monitoring" to begin helmet detection</p>
      </div>
    </div>

    <div class="alert-section">
      <h3>Recent Alerts</h3>
      <div id="alertsList">
        <p>No recent alerts</p>
      </div>
    </div>
  </div>

  <script>
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const feedsDiv = document.getElementById('feeds');

    let isStreaming = false;

    function updateStatus(active) {
      if (active) {
        statusDot.classList.add('active');
        statusText.textContent = 'System Active - Monitoring Safety';
      } else {
        statusDot.classList.remove('active');
        statusText.textContent = 'System Inactive';
      }
    }

    function loadStats() {
      fetch('/stats')
        .then(response => response.json())
        .then(data => {
          document.getElementById('totalDetections').textContent = data.total_detections || 0;
          document.getElementById('helmetDetections').textContent = data.helmet_detections || 0;
          document.getElementById('noHelmetDetections').textContent = data.no_helmet_detections || 0;
          document.getElementById('activeAlerts').textContent = data.active_alerts || 0;
        })
        .catch(error => console.error('Error loading stats:', error));
    }

    startBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/start-helmet_detection', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
          isStreaming = true;
          updateStatus(true);
          feedsDiv.innerHTML = '<p>Helmet detection monitoring started successfully</p>';
          alert('Helmet detection started successfully!');
          loadStats();
        } else {
          alert('Failed to start: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });

    stopBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/stop-stream', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
          isStreaming = false;
          updateStatus(false);
          feedsDiv.innerHTML = '<p>Click "Start Monitoring" to begin helmet detection</p>';
          alert('Helmet detection stopped successfully!');
        } else {
          alert('Failed to stop: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });

    // Load initial stats
    loadStats();
    
    // Refresh stats every 30 seconds
    setInterval(loadStats, 30000);
  </script>
</body>
</html>
