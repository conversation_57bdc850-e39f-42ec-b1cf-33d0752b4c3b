from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import requests

# Import quality control modules
from .database import get_db, create_tables
from .config import settings
from .routes import router as quality_control_router
from .middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Quality Control Service", version="1.0.0")

# Create database tables
create_tables()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.middleware("http")(auth_middleware)

# Setup templates and static files
templates = Jinja2Templates(directory="quality_control_service/templates")
app.mount("/static", StaticFiles(directory="quality_control_service/static"), name="static")

# Include quality control routes
app.include_router(quality_control_router, prefix="", tags=["Quality Control"])

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "quality_control"}

# Root endpoint
@app.get("/", response_class=HTMLResponse)
async def quality_control_home(request: Request):
    return templates.TemplateResponse("quality_control.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
