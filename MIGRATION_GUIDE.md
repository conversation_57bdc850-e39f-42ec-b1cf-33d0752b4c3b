# VigilanteEye Module Separation Migration Guide

## Overview

This guide explains how to complete the migration from a monolithic FastAPI application to separate module-based FastAPI applications.

## Target Architecture

```
vigilanteye/
├── authentication/
│   ├── app/
│   │   ├── main.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── models.py
│   │   ├── auth.py
│   │   ├── admin.py
│   │   └── email_utils.py
│   ├── templates/
│   ├── static/
│   └── requirements.txt
├── face_recognition/
│   ├── app/
│   │   ├── main.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── models.py
│   │   ├── auth.py
│   │   ├── admin.py
│   │   ├── email_utils.py
│   │   └── middleware.py
│   ├── routes.py
│   ├── utils.py
│   ├── services/
│   ├── templates/
│   ├── static/
│   ├── models/
│   └── requirements.txt
├── crowd_detection/
│   ├── app/
│   ├── routes.py
│   ├── utils.py
│   ├── templates/
│   ├── static/
│   ├── models/
│   └── requirements.txt
├── helmet_detection/
│   ├── app/
│   ├── routes.py
│   ├── utils.py
│   ├── templates/
│   ├── static/
│   ├── models/
│   └── requirements.txt
├── quality_control/
│   ├── app/
│   ├── routes.py
│   ├── utils.py
│   ├── templates/
│   ├── static/
│   ├── model/
│   └── requirements.txt
└── startup scripts...
```

## Migration Steps

### Step 1: Move Modules Out of App Folder

1. **Move face_recognition module:**
   ```bash
   mv app/face_recognition ./face_recognition
   ```

2. **Move crowd_detection module:**
   ```bash
   mv app/crowd_detection ./crowd_detection
   ```

3. **Move helmet_detection module:**
   ```bash
   mv app/helmet_detection ./helmet_detection
   ```

4. **Move quality_control module:**
   ```bash
   mv app/quality_control ./quality_control
   ```

### Step 2: Create App Folders for Each Module

For each module, create an `app` folder and move the main application files:

```bash
# For each module (face_recognition, crowd_detection, helmet_detection, quality_control)
mkdir {module_name}/app
```

### Step 3: Create Main.py for Each Module

Copy the template main.py files I've created and customize them for each module:

1. **Face Recognition:** Use `face_recognition/app/main.py` as template
2. **Crowd Detection:** Create similar structure
3. **Helmet Detection:** Create similar structure  
4. **Quality Control:** Create similar structure

### Step 4: Create Database Configurations

For each module, create:
- `config.py` - Database and app configuration
- `database.py` - Database connection and session management
- `models.py` - Database models specific to the module

### Step 5: Copy Authentication Files

Each module needs its own authentication system. Copy these files to each module's `app/` folder:
- `auth.py`
- `admin.py`
- `email_utils.py`
- `models.py` (auth models)

### Step 6: Create Middleware

Create `middleware.py` in each module's `app/` folder for authentication middleware.

### Step 7: Update Import Statements

Update all import statements in the moved files:

**Before:**
```python
from app.face_recognition.routes import router
from app.core.auth import authenticate_user
```

**After:**
```python
from ..routes import router
from .auth import authenticate_user
```

### Step 8: Create Requirements Files

Create `requirements.txt` for each module with their specific dependencies.

### Step 9: Create Login Templates

Create login templates for each module in their respective `templates/` folders.

### Step 10: Test Each Module

Start each module individually and test functionality:

```bash
python start_face_recognition.py
python start_crowd_detection.py
python start_helmet_detection.py
python start_quality_control.py
python start_authentication.py
```

## What I've Already Created

### ✅ Authentication Module
- Complete authentication service in `authentication/` folder
- All auth-related files copied and configured
- Login templates created
- Requirements file created

### ✅ Startup Scripts
- Individual startup scripts for each module
- Master startup script (`start_all_modules.py`)

### ✅ Face Recognition Template
- Started face recognition module structure
- Template main.py file created
- Database configuration template

## What You Need to Complete

### 1. Move the Modules
Move the 4 modules out of the `app/` folder to the root level.

### 2. Complete Each Module Structure
For each module, you need to:
- Create the `app/` folder structure
- Copy and adapt the main.py template
- Create database configurations
- Copy authentication files
- Update import statements
- Create login templates

### 3. Database Separation (Optional)
If you want separate databases for each module:
- Update the database configurations
- Create separate database names
- Run migrations for each module

### 4. Test and Debug
- Start each module individually
- Test all functionality
- Fix any import or configuration issues

## Benefits After Migration

1. **Complete Independence:** Each module runs as a separate FastAPI application
2. **Individual Deployment:** Deploy modules on different servers/ports
3. **Scalability:** Scale modules independently based on load
4. **Maintainability:** Smaller, focused codebases
5. **Technology Flexibility:** Each module can use different technologies

## Support

The authentication module is complete and ready to use. Use it as a reference for creating the other modules. Each module should follow the same pattern:

1. FastAPI app in `app/main.py`
2. Database configuration in `app/config.py`
3. Authentication system in `app/auth.py`
4. Routes imported from the module's `routes.py`
5. Templates and static files in module folders

This migration will give you complete module independence while preserving all existing functionality.
