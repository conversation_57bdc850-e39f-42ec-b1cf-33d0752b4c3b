from fastapi import <PERSON><PERSON><PERSON>, Request, Form, Depends, status, HTTPException, Body
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordR<PERSON><PERSON>Form
from datetime import timedelta
from sqlalchemy.orm import Session
from app.helmet_detection.routes import router as helmet_detection_router
from app.crowd_detection.routes import router as crowd_detection_router
from app.quality_control.routes import router as quality_control_router
from app.face_recognition.routes import router as face_recognition_router

# Import core modules
from app.core.auth import (
    authenticate_user, create_access_token,
    get_user, create_user, get_user_by_email
)
from app.face_recognition.database import get_db, get_auth_db, create_tables
from app.face_recognition.config import settings
from app.core.models import Token, OTPCreate, OTPVerify, User
from app.core.email_utils import create_otp, verify_otp, send_otp_email

# Import middleware
from app.middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI()

# Create database tables if they don't exist
create_tables()

# Create auth database tables and default users (admin and regular user)
from app.face_recognition.database import create_auth_tables
from app.core.admin import setup_default_users
create_auth_tables()
setup_default_users()

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules
app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
# app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

# Create the directory if it doesn't exist
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include routers
app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])



# Home page - protected by middleware
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

# Admin user management page - protected by middleware
@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request, db: Session = Depends(get_auth_db)):
    # Get all users from the database
    users = db.query(User).all()
    return templates.TemplateResponse(
        "admin_users.html",
        {"request": request, "users": users}
    )

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    # The registered parameter is handled directly in the template
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1  # 30 days if remember is checked, 1 day otherwise
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# Logout
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# API token endpoint for programmatic access
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

# Registration page
@app.get("/register", response_class=HTMLResponse)
async def register_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})

# Step 1: Request OTP for registration
@app.post("/request-otp", response_class=JSONResponse)
async def request_otp(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: Session = Depends(get_auth_db)
):
    # Check if passwords match
    if password != confirm_password:
        return JSONResponse(
            status_code=400,
            content={"error": "Passwords do not match"}
        )

    # Check if username already exists
    existing_user = get_user(db, username)
    if existing_user:
        return JSONResponse(
            status_code=400,
            content={"error": "Username already registered"}
        )

    # Check if email already exists
    existing_email = get_user_by_email(db, email)
    if existing_email:
        return JSONResponse(
            status_code=400,
            content={"error": "Email already registered"}
        )

    # Create OTP and send email
    try:
        otp = create_otp(db, email)
        success = send_otp_email(otp)

        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to send verification email"}
            )

        # Store registration data in session or return to client
        # For simplicity, we'll return a success message and expect the frontend to handle the flow
        return JSONResponse(
            content={
                "message": "Verification code sent to admin email. Please enter the code to complete registration.",
                "email": email
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"OTP generation failed: {str(e)}"}
        )

# Step 2: Verify OTP and complete registration
@app.post("/verify-otp", response_class=JSONResponse)
async def verify_otp_and_register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    otp_code: str = Form(...),
    db: Session = Depends(get_auth_db)
):
    # Verify OTP
    is_valid = verify_otp(db, email, otp_code)

    if not is_valid:
        return JSONResponse(
            status_code=400,
            content={"error": "Invalid or expired verification code"}
        )

    # Create the user
    try:
        create_user(db, username, email, password, full_name)
        return JSONResponse(
            content={
                "message": "Registration successful! You can now log in.",
                "redirect": "/login"
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Registration failed: {str(e)}"}
        )

# Legacy registration endpoint (now with OTP verification)
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    otp_code: str = Form(None),
    db: Session = Depends(get_auth_db)
):
    # Debug information
    print(f"Registration attempt: Username={username}, Email={email}, OTP Code={otp_code}")

    # Check if passwords match
    if password != confirm_password:
        print("Passwords do not match")
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": "Passwords do not match"}
        )

    # Check if username already exists
    existing_user = get_user(db, username)
    if existing_user:
        print(f"Username {username} already exists")
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": "Username already registered"}
        )

    # Check if email already exists
    existing_email = get_user_by_email(db, email)
    if existing_email:
        print(f"Email {email} already exists")
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": "Email already registered"}
        )

    # If OTP code is not provided or is empty, send OTP and show verification form
    if not otp_code or otp_code.strip() == "":
        print("No OTP provided, generating new OTP")
        otp = create_otp(db, email)
        success = send_otp_email(otp)

        if not success:
            print("Failed to send verification email")
            return templates.TemplateResponse(
                "register.html",
                {"request": request, "error": "Failed to send verification email"}
            )

        print(f"OTP sent to admin email: {otp.code}")
        # Store the actual password in the session or as a hidden field
        # For security, we should encrypt this in a real production environment
        # But for this demo, we'll pass it directly (not recommended for production)
        return templates.TemplateResponse(
            "register.html",
            {
                "request": request,
                "message": "Verification code sent to admin email. Please enter the code to complete registration.",
                "show_otp": True,
                "username": username,
                "email": email,
                "full_name": full_name,
                "original_password": password  # Pass the original password to be stored in hidden field
            }
        )

    # Verify OTP
    print(f"Verifying OTP: {otp_code} for email: {email}")
    is_valid = verify_otp(db, email, otp_code)

    if not is_valid:
        print("Invalid or expired verification code")
        return templates.TemplateResponse(
            "register.html",
            {
                "request": request,
                "error": "Invalid or expired verification code",
                "show_otp": True,
                "username": username,
                "email": email,
                "full_name": full_name,
                "original_password": password  # Preserve the original password
            }
        )

    # Create the user
    try:
        print(f"Creating user: {username}, {email}")
        create_user(db, username, email, password, full_name)
        # Redirect to login page with success message
        print("User created successfully, redirecting to login page")
        response = RedirectResponse(url="/login?registered=true", status_code=status.HTTP_302_FOUND)
        return response
    except Exception as e:
        print(f"Registration failed: {str(e)}")
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": f"Registration failed: {str(e)}"}
        )