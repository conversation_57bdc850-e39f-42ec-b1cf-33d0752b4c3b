import os
import shutil
from pathlib import Path

def copy_missing_images():
    """
    Copy cropped face images to static/images directory for users that don't have an original image.
    This ensures that all users have a profile picture in the static/images directory.
    """
    # Ensure the static/images directory exists
    os.makedirs("static/images", exist_ok=True)
    
    # Get all cropped face images
    cropped_faces_dir = Path("cropped_faces")
    if not cropped_faces_dir.exists():
        print("Cropped faces directory not found.")
        return
    
    # Get all images in the static/images directory
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]
    
    # Copy missing images
    copied_count = 0
    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)
            copied_count += 1
    
    print(f"Copied {copied_count} images to static/images directory.")

if __name__ == "__main__":
    copy_missing_images()
