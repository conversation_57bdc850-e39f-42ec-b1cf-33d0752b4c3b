#!/usr/bin/env python3
"""
Startup script for Authentication Module
Run this to start the authentication module on port 8000
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Authentication Module on http://localhost:8000")
    print("This module handles centralized authentication")
    print("Access module at: http://localhost:8000")
    print("Login at: http://localhost:8000/login")
    print("=" * 50)
    
    uvicorn.run(
        "authentication.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
