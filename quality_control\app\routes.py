from fastapi import APIRouter, WebSocket, Request, HTTPException, Depends
from typing import Optional
from fastapi.templating import Jin<PERSON>2Templates
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
import cv2
import os
from sqlalchemy.orm import Session
from database import get_db
from models import Inspection, Defect, ProductBatch, QualityStandard, Camera
from datetime import datetime, timezone

router = APIRouter()

templates = Jinja2Templates(directory="quality_control/templates")

@router.get("/quality_control")
async def show_quality_control_page(request: Request):
    # Get user role from request state (set by middleware)
    user_role = getattr(request.state, "user", {}).get("role", "user")
    return templates.TemplateResponse("quality_control.html", {"request": request, "user_role": user_role})

# Global variable for quality control system
qc_system = None

class QualityControlSystem:
    def __init__(self):
        self.running = False
        self.cameras = {}
        self.qc_frames = []
        self.camera_names = []
        
    def start(self, camera_details):
        """Start quality control inspection"""
        self.running = True
        self.cameras = camera_details
        self.camera_names = list(camera_details.keys())
        self.qc_frames = [None] * len(self.camera_names)
        print(f"Quality control started with {len(self.camera_names)} cameras")
        
    def stop(self):
        """Stop quality control inspection"""
        self.running = False
        self.qc_frames = []
        print("Quality control stopped")

def get_qc_system():
    global qc_system
    if qc_system is None:
        qc_system = QualityControlSystem()
    return qc_system

# API to start quality control
@router.post("/start-quality_control")
async def start_inspection(request: Request, db: Session = Depends(get_db)):
    system = get_qc_system()
    
    if not system.running:
        camera_details = load_cameras(db)
        system.start(camera_details)

    return {"status": "quality control started"}

# API to stop quality control
@router.post("/stop-inspection")
async def stop_inspection():
    system = get_qc_system()
    if system.running:
        system.stop()
    return {"status": "quality control stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            system = get_qc_system()
            if system and system.running:
                for idx in range(len(system.qc_frames)):
                    if system.qc_frames[idx] is not None:
                        cam_name = system.camera_names[idx] if idx < len(system.camera_names) else f"Camera {idx}"
                        frame = system.qc_frames[idx]
                        
                        # Send camera info and frame
                        await websocket.send_text(f"{idx}:{cam_name}:0:0")  # Placeholder counts
                        await websocket.send_bytes(frame)
                        
                await asyncio.sleep(0.03)
            else:
                cv2.destroyAllWindows()
                await asyncio.sleep(0.5)
    except Exception as e:
        print("WebSocket disconnected:", e)

# Database-based camera management
def load_cameras(db: Session):
    """Load cameras from database"""
    cameras = db.query(Camera).filter(Camera.is_active == True).all()
    result = {}
    for camera in cameras:
        result[camera.name] = [camera.rtsp_url, camera.inspection_zone or []]
    return result

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData, db: Session = Depends(get_db)):
    # Check if the camera name already exists
    existing_camera = db.query(Camera).filter(Camera.name == camera_data.cameraName).first()
    if existing_camera:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists
    existing_url = db.query(Camera).filter(Camera.rtsp_url == camera_data.rtspUrl).first()
    if existing_url:
        return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_url.name}"}

    # Add new camera
    try:
        new_camera = Camera(
            name=camera_data.cameraName,
            rtsp_url=camera_data.rtspUrl,
            camera_type='overhead',
            is_active=True
        )
        db.add(new_camera)
        db.commit()
        return {"status": "success", "message": "Camera added successfully"}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": f"Failed to add camera: {str(e)}"}

# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str, db: Session = Depends(get_db)):
    camera = db.query(Camera).filter(Camera.name == camera_name).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Soft delete - mark as inactive
    camera.is_active = False
    db.commit()
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}

@router.get("/get-cameras")
async def get_cameras(db: Session = Depends(get_db)):
    cameras = load_cameras(db)
    return cameras

# Inspection endpoints
@router.get("/inspections")
async def get_inspections(db: Session = Depends(get_db)):
    """Get recent inspections"""
    inspections = db.query(Inspection).order_by(Inspection.timestamp.desc()).limit(100).all()
    return inspections

@router.get("/defects")
async def get_defects(db: Session = Depends(get_db)):
    """Get recent defects"""
    defects = db.query(Defect).order_by(Defect.id.desc()).limit(50).all()
    return defects

@router.get("/batches")
async def get_batches(db: Session = Depends(get_db)):
    """Get product batches"""
    batches = db.query(ProductBatch).order_by(ProductBatch.start_time.desc()).limit(20).all()
    return batches

@router.get("/quality-standards")
async def get_quality_standards(db: Session = Depends(get_db)):
    """Get quality standards"""
    standards = db.query(QualityStandard).filter(QualityStandard.is_active == True).all()
    return standards

# Statistics endpoint
@router.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get quality control statistics"""
    from datetime import datetime, timedelta
    
    # Get stats for the last 24 hours
    yesterday = datetime.now() - timedelta(days=1)
    
    total_inspections = db.query(Inspection).filter(Inspection.timestamp >= yesterday).count()
    passed_inspections = db.query(Inspection).filter(
        Inspection.timestamp >= yesterday,
        Inspection.result == 'pass'
    ).count()
    failed_inspections = db.query(Inspection).filter(
        Inspection.timestamp >= yesterday,
        Inspection.result == 'fail'
    ).count()
    total_defects = db.query(Defect).join(Inspection).filter(
        Inspection.timestamp >= yesterday
    ).count()
    
    # Calculate pass rate
    pass_rate = (passed_inspections / total_inspections * 100) if total_inspections > 0 else 0
    
    return {
        "total_inspections": total_inspections,
        "passed_inspections": passed_inspections,
        "failed_inspections": failed_inspections,
        "total_defects": total_defects,
        "pass_rate": round(pass_rate, 2)
    }
