from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey, DateTime, JSON, Boolean
from app.face_recognition.database import Base
from sqlalchemy.orm import relationship
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import UploadFile


class User(Base):
    __tablename__ = 'users'

    user_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(String(50), unique=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    department = Column(String(100), nullable=True)
    dob = Column(String(20), nullable=True)
    address = Column(String(255), nullable=True)
    phone_number = Column(String(20), nullable=True)

    images = relationship("Image", back_populates="user", cascade="all, delete-orphan")
    attendances = relationship("Attendance", back_populates="user", cascade="all, delete-orphan")
    encodings = relationship("Encoding", back_populates="user", cascade="all, delete-orphan")
    camera_permissions = relationship("CameraPermission", back_populates="user", cascade="all, delete-orphan")

class Encoding(Base):
    __tablename__ = 'encodings'

    id = Column(Integer, primary_key=True, index=True)  # Added primary key
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    encoding = Column(JSON, nullable=False)
    image_id = Column(Integer, ForeignKey('images.id'))  # Fixed foreign key reference

    user = relationship("User", back_populates="encodings")
    image = relationship("Image", back_populates="encodings")

    def __repr__(self):
        return f"<Encoding(id={self.id}, user_id={self.user_id}, encoding_size={len(self.encoding)})>"


class Image(Base):
    __tablename__ = 'images'

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(100), index=True)
    image_url = Column(String(255), nullable=True)

    user_id = Column(Integer, ForeignKey('users.user_id'))

    user = relationship("User", back_populates="images")
    encodings = relationship("Encoding", back_populates="image")


class Attendance(Base):
    __tablename__ = 'attendances'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    user_id = Column(Integer, ForeignKey('users.user_id'))
    camera_name = Column(String(100), nullable=True)  # Added camera name field
    user = relationship("User", back_populates="attendances")

class UnknownAttendance(Base):
    __tablename__ = 'unknown_attendances'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    unknown_id = Column(Integer, ForeignKey('unknowns.id'))
    camera_name = Column(String(100), nullable=True)
    image_path = Column(String(255), nullable=True)  # Added image path field

    unknown = relationship("Unknown")


class Unknown(Base):
    __tablename__ = 'unknowns'

    id = Column(Integer, primary_key=True, index=True)
    persistent_id = Column(String(64), unique=True)
    encoding = Column(JSON, nullable=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    cluster_id = Column(String(100), nullable=True, index=True)  # Added for HDBSCAN clustering
    image_path = Column(String(255), nullable=True)  # Added image path field

    def __repr__(self):
        return f"<Unknown(persistent_id={self.persistent_id}, timestamp={self.timestamp}, cluster_id={self.cluster_id})>"


class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), nullable=False)

    def __repr__(self):
        return f"<Camera(id={self.id}, name={self.name})>"


class CameraPermission(Base):
    __tablename__ = 'camera_permissions'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    camera_id = Column(Integer, ForeignKey('cameras.id'), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="camera_permissions")
    camera = relationship("Camera", back_populates="user_permissions")

    def __repr__(self):
        return f"<CameraPermission(id={self.id}, user_id={self.user_id}, camera_id={self.camera_id})>"


class Webhook(Base):
    __tablename__ = 'webhooks'

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String(255), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    body_template = Column(JSON, nullable=True)  # Store Jinja template as JSON
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Webhook(id={self.id}, url={self.url}, active={self.is_active})>"

class WhatsApp(Base):
    __tablename__ = 'whatsapp'

    id = Column(Integer, primary_key=True, index=True)
    phone_number = Column(String(20), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<WhatsApp(id={self.id}, phone_number={self.phone_number}, full_name={self.full_name}, active={self.is_active})>"

class SMS(Base):
    __tablename__ = 'sms'

    id = Column(Integer, primary_key=True, index=True)
    phone_number = Column(String(20), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<SMS(id={self.id}, phone_number={self.phone_number}, full_name={self.full_name}, active={self.is_active})>"

class Email(Base):
    __tablename__ = 'email'

    id = Column(Integer, primary_key=True, index=True)
    email_address = Column(String(100), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Email(id={self.id}, email_address={self.email_address}, full_name={self.full_name}, active={self.is_active})>"

# Update User model to include the relationship
User.camera_permissions = relationship("CameraPermission", back_populates="user", cascade="all, delete-orphan")

# Update Camera model to include the relationship
Camera.user_permissions = relationship("CameraPermission", back_populates="camera", cascade="all, delete-orphan")



# Pydantic models for API requests/responses
class UserCreate(BaseModel):
    username: str
    email: str
    image_urls: List[UploadFile] = []
    employee_id: str = None
    department: str = None
    dob: str = None
    address: str = None
    phone_number: str = None


class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    employee_id: Optional[str] = None
    department: Optional[str] = None
    dob: Optional[str] = None
    address: Optional[str] = None
    phone_number: Optional[str] = None


class AttendanceCreate(BaseModel):
    user_id: int
    timestamp: datetime


class EncodingCreate(BaseModel):
    encoding: List[float]
    user_id: int
    image_id: Optional[int] = None


class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str


class ClusteringParams(BaseModel):
    min_cluster_size: int = 2
    min_samples: Optional[int] = None


class CameraPermissionCreate(BaseModel):
    user_id: int
    camera_id: int

class CameraPermissionResponse(BaseModel):
    id: int
    user_id: int
    camera_id: int
    created_at: datetime

    class Config:
        orm_mode = True


class WebhookCreate(BaseModel):
    url: str
    description: Optional[str] = None
    body_template: Optional[dict] = None

class WebhookUpdate(BaseModel):
    url: Optional[str] = None
    description: Optional[str] = None
    body_template: Optional[dict] = None
    is_active: Optional[bool] = None

class WebhookResponse(BaseModel):
    id: int
    url: str
    description: Optional[str]
    body_template: Optional[dict]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class WhatsAppCreate(BaseModel):
    phone_number: str
    full_name: str

class WhatsAppUpdate(BaseModel):
    phone_number: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class WhatsAppResponse(BaseModel):
    id: int
    phone_number: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class SMSCreate(BaseModel):
    phone_number: str
    full_name: str

class SMSUpdate(BaseModel):
    phone_number: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class SMSResponse(BaseModel):
    id: int
    phone_number: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class EmailCreate(BaseModel):
    email_address: str
    full_name: str

class EmailUpdate(BaseModel):
    email_address: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class EmailResponse(BaseModel):
    id: int
    email_address: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True