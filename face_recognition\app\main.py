from fastapi import <PERSON><PERSON><PERSON>, Request, Form, Depends, status, HTTPException, Body
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordR<PERSON><PERSON>Form
from datetime import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
from pathlib import Path
import sys

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import face recognition modules
from routes import router as face_recognition_router
from database import get_db, create_tables
from config import settings

# Import auth client for API communication
from auth_client import auth_client

# Import middleware
from middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Face Recognition Service", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# Note: Authentication is handled by separate auth service

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="face_recognition/templates")

# Create static directory if it doesn't exist
import os
static_dir = "face_recognition/static"
if not os.path.exists(static_dir):
    os.makedirs(static_dir, exist_ok=True)

dataset_dir = "Dataset"
if not os.path.exists(dataset_dir):
    os.makedirs(dataset_dir, exist_ok=True)

app.mount("/static", StaticFiles(directory=static_dir), name="static")
app.mount("/Dataset", StaticFiles(directory=dataset_dir), name="dataset")

# Mount the static/images directory for user profile images
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("app/cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include face recognition routes
app.include_router(face_recognition_router, prefix="", tags=["Face Recognition"])

# Home page - protected by middleware
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("face_recognition.html", {"request": request})

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False)
):
    # Authenticate via auth service
    auth_result = auth_client.authenticate_user(username, password)

    if not auth_result or not auth_result.get("success"):
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Get access token from auth service
    access_token = auth_client.create_token(username, password)

    if not access_token:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Authentication failed"}
        )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    expires_days = 30 if remember else 1
    max_age = expires_days * 24 * 60 * 60  # Convert to seconds

    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=max_age,
        expires=max_age,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# Logout
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# API token endpoint - redirect to auth service
@app.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    # Use auth service to create token
    access_token = auth_client.create_token(form_data.username, form_data.password)
    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return {"access_token": access_token, "token_type": "bearer"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
