from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
from pathlib import Path
import sys

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import face recognition modules
from routes import router as face_recognition_router
from database import get_db, create_tables
from config import settings

# Import auth client for API communication
from auth_client import auth_client

# Import middleware
from middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI(title="Face Recognition Service", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# Note: Authentication is handled by separate auth service

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "templates")
if not os.path.exists(templates_dir):
    os.makedirs(templates_dir, exist_ok=True)

templates = Jinja2Templates(directory=templates_dir)

# Create static directory if it doesn't exist
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir, exist_ok=True)

dataset_dir = "Dataset"
if not os.path.exists(dataset_dir):
    os.makedirs(dataset_dir, exist_ok=True)

# Mount static directories
app.mount("/static", StaticFiles(directory=static_dir), name="static")
app.mount("/Dataset", StaticFiles(directory=dataset_dir), name="dataset")

# Mount the static/images directory for user profile images
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("app/cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include face recognition routes
app.include_router(face_recognition_router, prefix="", tags=["Face Recognition"])

# Home page - direct access, no authentication required
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("face_recognition.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
