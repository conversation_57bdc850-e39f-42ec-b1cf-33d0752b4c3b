from fastapi import API<PERSON>outer, WebSocket, Request, HTTPException, Depends
from typing import Optional
from fastapi.templating import <PERSON><PERSON>2Templates
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
import cv2
import os
from sqlalchemy.orm import Session
from database import get_db
from models import CrowdEvent, Alert, Camera, CrowdEventCreate, AlertCreate, CameraCreate
from datetime import datetime, timezone

router = APIRouter()

templates = Jinja2Templates(directory="crowd_detection/templates")

@router.get("/crowd_detection")
async def show_crowd_detection_page(request: Request):
    # Get user role from request state (set by middleware)
    user_role = getattr(request.state, "user", {}).get("role", "user")
    return templates.TemplateResponse("crowd_detection.html", {"request": request, "user_role": user_role})

# Global variable for crowd detection system
crowd_detector = None

class CrowdDetection:
    def __init__(self):
        self.running = False
        self.cameras = {}
        self.crowd_frames = []
        self.camera_names = []
        
    def start(self, camera_details):
        """Start crowd detection"""
        self.running = True
        self.cameras = camera_details
        self.camera_names = list(camera_details.keys())
        self.crowd_frames = [None] * len(self.camera_names)
        print(f"Crowd detection started with {len(self.camera_names)} cameras")
        
    def stop(self):
        """Stop crowd detection"""
        self.running = False
        self.crowd_frames = []
        print("Crowd detection stopped")

def get_crowd_detector():
    global crowd_detector
    if crowd_detector is None:
        crowd_detector = CrowdDetection()
    return crowd_detector

# API to start crowd detection
@router.post("/start-crowd_detection")
async def start_stream(request: Request, db: Session = Depends(get_db)):
    detector = get_crowd_detector()
    
    if not detector.running:
        camera_details = load_cameras(db)
        detector.start(camera_details)

    return {"status": "crowd detection started"}

# API to stop crowd detection
@router.post("/stop-stream")
async def stop_stream():
    detector = get_crowd_detector()
    if detector.running:
        detector.stop()
    return {"status": "crowd detection stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            detector = get_crowd_detector()
            if detector and detector.running:
                for idx in range(len(detector.crowd_frames)):
                    if detector.crowd_frames[idx] is not None:
                        cam_name = detector.camera_names[idx] if idx < len(detector.camera_names) else f"Camera {idx}"
                        frame = detector.crowd_frames[idx]
                        
                        # Send camera info and frame
                        await websocket.send_text(f"{idx}:{cam_name}:0:0")  # Placeholder counts
                        await websocket.send_bytes(frame)
                        
                await asyncio.sleep(0.03)
            else:
                cv2.destroyAllWindows()
                await asyncio.sleep(0.5)
    except Exception as e:
        print("WebSocket disconnected:", e)

# Database-based camera management
def load_cameras(db: Session):
    """Load cameras from database"""
    cameras = db.query(Camera).filter(Camera.is_active == True).all()
    result = {}
    for camera in cameras:
        result[camera.name] = [camera.rtsp_url, camera.roi_coordinates or []]
    return result

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData, db: Session = Depends(get_db)):
    # Check if the camera name already exists
    existing_camera = db.query(Camera).filter(Camera.name == camera_data.cameraName).first()
    if existing_camera:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists
    existing_url = db.query(Camera).filter(Camera.rtsp_url == camera_data.rtspUrl).first()
    if existing_url:
        return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_url.name}"}

    # Add new camera
    try:
        new_camera = Camera(
            name=camera_data.cameraName,
            rtsp_url=camera_data.rtspUrl,
            is_active=True
        )
        db.add(new_camera)
        db.commit()
        return {"status": "success", "message": "Camera added successfully"}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": f"Failed to add camera: {str(e)}"}

# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str, db: Session = Depends(get_db)):
    camera = db.query(Camera).filter(Camera.name == camera_name).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Soft delete - mark as inactive
    camera.is_active = False
    db.commit()
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}

@router.get("/get-cameras")
async def get_cameras(db: Session = Depends(get_db)):
    cameras = load_cameras(db)
    return cameras

# Crowd event endpoints
@router.get("/crowd-events")
async def get_crowd_events(db: Session = Depends(get_db)):
    """Get recent crowd events"""
    events = db.query(CrowdEvent).order_by(CrowdEvent.timestamp.desc()).limit(100).all()
    return events

@router.get("/alerts")
async def get_alerts(db: Session = Depends(get_db)):
    """Get recent alerts"""
    alerts = db.query(Alert).order_by(Alert.timestamp.desc()).limit(50).all()
    return alerts

@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: int, db: Session = Depends(get_db)):
    """Mark an alert as resolved"""
    alert = db.query(Alert).filter(Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="Alert not found")
    
    alert.is_resolved = True
    alert.resolved_at = datetime.now(timezone.utc)
    db.commit()
    
    return {"status": "success", "message": "Alert resolved"}

# Statistics endpoint
@router.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get crowd detection statistics"""
    from datetime import datetime, timedelta
    
    # Get stats for the last 24 hours
    yesterday = datetime.now() - timedelta(days=1)
    
    total_events = db.query(CrowdEvent).filter(CrowdEvent.timestamp >= yesterday).count()
    high_density_events = db.query(CrowdEvent).filter(
        CrowdEvent.timestamp >= yesterday,
        CrowdEvent.alert_level.in_(['high', 'critical'])
    ).count()
    active_alerts = db.query(Alert).filter(Alert.is_resolved == False).count()
    
    # Get average crowd density
    avg_density = db.query(CrowdEvent).filter(CrowdEvent.timestamp >= yesterday).with_entities(
        db.func.avg(CrowdEvent.crowd_density)
    ).scalar() or 0
    
    return {
        "total_events": total_events,
        "high_density_events": high_density_events,
        "active_alerts": active_alerts,
        "average_density": round(float(avg_density), 2)
    }
