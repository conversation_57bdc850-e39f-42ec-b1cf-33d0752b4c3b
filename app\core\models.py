from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
from pydantic import BaseModel, EmailStr
from typing import Optional

# Import the AuthBase from database.py
from app.core.database import AuthBase
from app.core.config import settings

# User database model (in vigilanteye database)
class User(AuthBase):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="user")  # 'admin' or 'user'
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Pydantic models for API
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    role: Optional[str] = "user"

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    role: Optional[str] = None

class UserInDB(UserBase):
    id: int
    disabled: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class UserResponse(UserBase):
    id: int
    disabled: bool
    role: str

    class Config:
        orm_mode = True

# OTP model for verification
class OTP(AuthBase):
    __tablename__ = "otps"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), index=True)
    code = Column(String(6))
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(minutes=settings.OTP_EXPIRY_MINUTES))
    is_used = Column(Boolean, default=False)

    def is_valid(self):
        """Check if OTP is valid (not expired and not used)"""
        now = datetime.utcnow()
        is_not_used = not self.is_used
        is_not_expired = now < self.expires_at
        print(f"OTP validity check: is_not_used={is_not_used}, is_not_expired={is_not_expired}, now={now}, expires_at={self.expires_at}")
        return is_not_used and is_not_expired

# Token models
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# OTP Pydantic models
class OTPCreate(BaseModel):
    email: EmailStr

class OTPVerify(BaseModel):
    email: EmailStr
    code: str

class OTPResponse(BaseModel):
    email: str
    expires_at: datetime

    class Config:
        orm_mode = True
