import threading
import queue
import time
import base64
import cv2
import datetime
import logging
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
from ultralytics import YOLO
import models
from utils import (generate_encoding, calculate_cosine_similarity)
import numpy as np
import uuid
from models import Encoding, User
from database import SessionLocal
from scipy.spatial.distance import cosine
from sqlalchemy.orm import Session
from facenet_pytorch import InceptionResnetV1
from utils import load_cameras, save_person_image
from services.qdrant_service import QdrantService
import torch
from torchvision import transforms as torch_transforms

# Set up logging
logger = logging.getLogger(__name__)

class FaceDetection:
    def __init__(self, model_path, db: Session):
        # Check CUDA availability
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        try:
            # Try to load YOLO with CUDA
            self.model = YOLO(model_path)
        except Exception as e:
            # Fallback to CPU explicitly if there was an error
            self.model = YOLO(model_path)

        # Initialize FaceNet on GPU if available
        self.embedder = InceptionResnetV1(pretrained='vggface2').to(self.device).eval()

        # Rest of initialization
        self.db = db
        self.running = False
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        self.camera_threads = []
        self.process_threads = []
        self.lock = threading.Lock()
        self.cameraname = []
        self.db_encodings_cache = {}
        self.db_users_cache = {}
        self.threshold = None
        self.unknown_track_map = {}

        # Background task queues for non-blocking operations
        self.attendance_queue = queue.Queue()
        self.image_save_queue = queue.Queue()
        self.webhook_queue = queue.Queue()

        # Background worker threads
        self.attendance_worker = None
        self.image_worker = None
        self.webhook_worker = None
        self.workers_running = False

        # Initialize the Qdrant service
        try:
            self.qdrant_client = QdrantService()
        except Exception as e:
            self.qdrant_client = None

    def start(self, threshold=0.5):
        """Starts the face detection system."""
        if self.running:
            return

        # Initialize threshold with the provided value
        self.threshold = threshold

        # Cache database entries to avoid repeated queries
        self.db_encodings_cache = {enc.user_id: np.array(enc.encoding) for enc in self.db.query(Encoding).all()}
        self.db_users_cache = {user.user_id: user.username for user in self.db.query(User).all()}

        # Load cameras from the database
        try:
            # Check if the database session is active
            if not self.db.is_active:
                self.db = SessionLocal()

            # Load cameras with the refreshed session
            cameras = load_cameras(self.db)
            if not cameras:
                return

        except Exception as e:
            logger.error(f"Error loading cameras from database: {e}")
            return

        # Clear previous state
        self.stop_connections()

        # Get all camera names and URLs
        all_camera_names = list(cameras.keys())

        # Initialize cameras and keep track of which ones successfully open
        self.cameraname = []  # Will only contain names of successfully opened cameras
        successful_cameras = []

        # First, collect all camera URLs with their names
        camera_urls_with_names = []
        for cam_name in all_camera_names:
            url = cameras[cam_name][0]
            if url.isdigit():
                url = int(url)
            camera_urls_with_names.append((cam_name, url))

        # Try to initialize each camera
        for cam_name, cam_url in camera_urls_with_names:
            try:
                cap = cv2.VideoCapture(cam_url)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                if cap.isOpened():
                    logger.info(f"Camera {cam_name} ({cam_url}) opened successfully.")
                    self.cap_devices.append(cap)
                    self.frame_queues.append(queue.Queue(maxsize=10))
                    self.cameraname.append(cam_name)  # Only add name if camera opened successfully
                    successful_cameras.append(cam_name)
                else:
                    logger.error(f"Failed to open camera: {cam_name} ({cam_url})")
            except Exception as e:
                logger.error(f"Error initializing camera {cam_name} ({cam_url}): {e}")

        # Ensure frames array matches the number of successfully opened cameras
        self.frames = [None] * len(self.cap_devices)

        if not self.running and self.cap_devices:
            self.running = True

            # Start background worker threads
            self.start_background_workers()

            # Start threads for each camera
            for idx, cap in enumerate(self.cap_devices):
                read_thread = self.thread_pool.submit(self.read_frames, idx, cap)
                process_thread = self.thread_pool.submit(self.process_frames, idx)
                self.camera_threads.append(read_thread)
                self.process_threads.append(process_thread)

    def stop_connections(self):
        """Clean up camera connections without stopping the entire system."""
        for cap in self.cap_devices:
            if cap and cap.isOpened():
                cap.release()
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        self.cameraname = []  # Reset camera names list as well

    def stop(self):
        """Stops the face detection system."""
        if not self.running:
            return

        self.running = False

        # Stop background workers
        self.stop_background_workers()

        # Give threads time to exit gracefully
        time.sleep(0.5)

        # Release all camera resources
        self.stop_connections()

    def read_frames(self, idx, cap):
        """Reads frames from the camera and adds them to the queue."""
        reconnect_attempts = 0
        max_reconnect_attempts = 5
        
        while self.running:
            try:
                ret, frame = cap.read()
                if not ret:
                    reconnect_attempts += 1

                    if reconnect_attempts > max_reconnect_attempts:
                        break

                    time.sleep(1)  # Wait before retry
                    continue

                reconnect_attempts = 0  # Reset on successful frame

                # Resize frame for consistency
                frame = cv2.resize(frame, (1080, 780))

                with self.lock:
                    if idx < len(self.frame_queues):  # Make sure the index is still valid
                        if self.frame_queues[idx].full():
                            self.frame_queues[idx].get()  # Remove the oldest frame
                        self.frame_queues[idx].put(frame)
                    else:
                        break  # This camera is no longer in our list, exit thread

            except Exception as e:
                time.sleep(0.1)  # Prevent CPU spin in case of errors

    def process_frames(self, idx):
        """Processes frames and performs face detection and recognition."""
        # Basic implementation - can be expanded
        while self.running:
            try:
                if idx >= len(self.frame_queues):
                    break  # Exit if this camera is no longer valid

                if self.frame_queues[idx].empty():
                    time.sleep(0.01)  # Short sleep when no frames
                    continue

                frame = self.frame_queues[idx].get()
                
                # Basic face detection logic would go here
                # For now, just process the frame
                time.sleep(0.1)  # Simulate processing time

            except Exception as e:
                logger.error(f"Error in process_frames for camera {idx}: {e}")
                time.sleep(0.1)

    def generate_encoding(self, face_image):
        """Generate face encoding using FaceNet."""
        try:
            return generate_encoding(face_image)
        except Exception as e:
            logger.error(f"Error generating encoding: {e}")
            return None

    def start_background_workers(self):
        """Start background worker threads."""
        self.workers_running = True
        # Implementation for background workers would go here

    def stop_background_workers(self):
        """Stop background worker threads."""
        self.workers_running = False
        # Implementation for stopping background workers would go here
