/* Reset styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9fb;
    color: #333;
}

/* Header styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    height: 50px;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    letter-spacing: 1px;
}

/* Main content styles */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 60px;
    padding: 20px;
}

/* Clickable container style */
.box {
    width: 280px;
    height: 180px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    color: white;
    font-size: 1.4rem;
    font-weight: bold;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.box:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

/* Background gradients for different services */
.box.face {
    background: linear-gradient(135deg, #11b73a 0%, #ff5219 100%);
}

.box.helmet {
    background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
}

.box.crowd {
    background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
}

.box.quality {
    background: linear-gradient(135deg, #1345b8 0%, #d45b5b 100%);
}

/* Service Status Section */
.status-container {
    max-width: 800px;
    margin: 40px auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-container h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ddd;
}

.service-name {
    font-weight: 500;
    color: #333;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-indicator.online {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-indicator.offline {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Logout button styles */
.logout-btn {
    padding: 8px 15px;
    background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 126, 95, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
    }

    .box {
        width: 100%;
        max-width: 280px;
    }

    .logo-container {
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }
}
