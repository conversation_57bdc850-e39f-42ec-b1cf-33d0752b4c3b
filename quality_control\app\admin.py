from sqlalchemy.orm import Session
from auth import get_password_hash, get_user
from database import AuthSessionLocal, create_auth_tables
from models import AuthUser as User

def create_admin_user():
    """Create an admin user if it doesn't already exist."""
    create_auth_tables()
    db = AuthSessionLocal()

    try:
        admin = get_user(db, "admin")
        if admin:
            if admin.role != "admin":
                admin.role = "admin"
                db.commit()
                print("Updated existing admin user with admin role")
            else:
                print("Admin user already exists with admin role")
            return

        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="Administrator",
            hashed_password=get_password_hash("admin123"),
            role="admin",
            disabled=False
        )

        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        print("Admin user created successfully")
    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

def create_regular_user():
    """Create a regular user if it doesn't already exist."""
    create_auth_tables()
    db = AuthSessionLocal()

    try:
        regular_user = get_user(db, "user")
        if regular_user:
            if regular_user.role != "user":
                regular_user.role = "user"
                db.commit()
                print("Updated existing regular user with user role")
            else:
                print("Regular user already exists with user role")
            return

        user = User(
            username="user",
            email="<EMAIL>",
            full_name="Regular User",
            hashed_password=get_password_hash("user123"),
            role="user",
            disabled=False
        )

        db.add(user)
        db.commit()
        db.refresh(user)
        print("Regular user created successfully")
    except Exception as e:
        print(f"Error creating regular user: {e}")
        db.rollback()
    finally:
        db.close()

def setup_default_users():
    """Set up both admin and regular users."""
    create_admin_user()
    create_regular_user()

if __name__ == "__main__":
    setup_default_users()
