<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Service</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .service-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            transition: transform 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication Service</h1>
        
        <div class="service-info">
            <h2>Centralized Authentication</h2>
            <p>This service handles authentication for all VigilanteEye modules.</p>
            <p>Each module runs as a separate FastAPI application on different ports.</p>
        </div>

        <div class="modules-grid">
            <a href="http://localhost:8001" class="module-card" target="_blank">
                <h3>Face Recognition</h3>
                <p>Port 8001</p>
            </a>
            <a href="http://localhost:8002" class="module-card" target="_blank">
                <h3>Crowd Detection</h3>
                <p>Port 8002</p>
            </a>
            <a href="http://localhost:8003" class="module-card" target="_blank">
                <h3>Helmet Detection</h3>
                <p>Port 8003</p>
            </a>
            <a href="http://localhost:8004" class="module-card" target="_blank">
                <h3>Quality Control</h3>
                <p>Port 8004</p>
            </a>
        </div>

        <a href="/logout" class="logout-btn">Logout</a>
    </div>
</body>
</html>
