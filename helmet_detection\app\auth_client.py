import requests
from typing import Optional, Dict, Any
from config import settings

class AuthenticationClient:
    """Client to communicate with the authentication service"""
    
    def __init__(self, auth_service_url: str = None):
        if auth_service_url is None:
            auth_service_url = settings.AUTH_SERVICE_URL
        self.auth_service_url = auth_service_url.rstrip('/')
        self.session = requests.Session()
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify token with authentication service"""
        try:
            response = self.session.post(
                f"{self.auth_service_url}/api/verify-token",
                json={"token": token},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error verifying token: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with authentication service"""
        try:
            response = self.session.post(
                f"{self.auth_service_url}/api/authenticate",
                json={"username": username, "password": password},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error authenticating user: {e}")
            return None
    
    def get_user_info(self, token: str) -> Optional[Dict[str, Any]]:
        """Get user information from authentication service"""
        try:
            response = self.session.get(
                f"{self.auth_service_url}/api/user-info",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting user info: {e}")
            return None
    
    def create_token(self, username: str, password: str) -> Optional[str]:
        """Create access token via authentication service"""
        try:
            response = self.session.post(
                f"{self.auth_service_url}/token",
                data={"username": username, "password": password},
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("access_token")
            return None
        except Exception as e:
            print(f"Error creating token: {e}")
            return None

# Global auth client instance
auth_client = AuthenticationClient()
