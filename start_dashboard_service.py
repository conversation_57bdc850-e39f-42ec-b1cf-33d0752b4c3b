#!/usr/bin/env python3
"""
Startup script for Dashboard Service
Run this to start the main dashboard service on port 8005
"""

import uvicorn
import sys
import os

# Add the dashboard_service directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dashboard_service'))

if __name__ == "__main__":
    print("Starting Dashboard Service on http://localhost:8005")
    print("This is the main dashboard that routes to all other services")
    print("Access dashboard at: http://localhost:8005")
    print("=" * 50)
    print("Make sure the following services are running:")
    print("- Auth Service: http://localhost:8000")
    print("- Face Recognition: http://localhost:8001")
    print("- Crowd Detection: http://localhost:8002")
    print("- Helmet Detection: http://localhost:8003")
    print("- Quality Control: http://localhost:8004")
    print("=" * 50)
    
    uvicorn.run(
        "dashboard_service.app:app",
        host="0.0.0.0",
        port=8005,
        reload=True,
        log_level="info"
    )
