{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import threading\n", "from ultralytics import YOLO\n", "import numpy as np\n", "import concurrent.futures\n", "\n", "class HelmetDetection:\n", "    def __init__(self, object_model,pose_model,conf_value,pose_conf_value=0.5):\n", "        self.conf_value = conf_value\n", "        self.pose_conf_value = pose_conf_value\n", "        self.object_model = YOLO(object_model,task=\"detect\")  \n", "        self.pose_model = YOLO(pose_model,task=\"pose\")\n", "        self.threads = []\n", "        self.running = False\n", "        self.blank = cv2.imread(\"./static/black.jpg\")\n", "        self.blank = cv2.resize(self.blank, (640, 480))\n", "\n", "\n", "    def start(self, camera_details, test_video_path=None):\n", "        self.person_count,self.no_helmet_count,self.no_vest_count,self.no_gloves_count = 0,0,0,0\n", "        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []\n", "        dic = camera_details\n", "\n", "        # If test_video_path is provided, override camera details\n", "        if test_video_path:\n", "            self.camera_name.append(\"Test Video\")\n", "            cap = cv2.VideoCapture(test_video_path)\n", "            if cap.isOpened():\n", "                self.cap_devices.append(cap)\n", "            else:\n", "                self.cap_devices.append(None)\n", "        else:\n", "            for key, value in dic.items():\n", "                self.camera_name.append(key)\n", "                if value[0].isdigit():\n", "                    value = int(value[0])\n", "                    self.rtsp_url.append(value)\n", "                    cap = cv2.VideoCapture(value)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "                else:\n", "                    cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "\n", "        self.helmet_frames = [None] * len(self.cap_devices)\n", "        self.warning_count = [{\"person_count\": 0, \"no_helmet_count\": 0, \"no_vest_count\": 0, \"no_gloves_count\": 0} for _ in range(len(self.cap_devices))]\n", "        # Create locks for each camera\n", "        self.locks = [threading.Lock() for _ in range(len(self.cap_devices))]\n", "\n", "        if not self.running:\n", "            self.running = True\n", "            for idx, cap in enumerate(self.cap_devices):\n", "                if cap is not None and cap.isOpened():\n", "                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))\n", "                    thread.daemon = True\n", "                    thread.start()\n", "                    self.threads.append(thread)\n", "                else:\n", "                    temp = cv2.putText(self.blank,f\"{self.camera_name[idx]} is Offline\",(35, 170),\n", "                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,\n", "                        fontScale=1,\n", "                        thickness=3,\n", "                        color=(255, 255, 255),\n", "                    )\n", "                    _, temp = cv2.imencode(\".png\", temp)\n", "                    with self.locks[idx]:  # Lock when updating shared resource\n", "                        self.helmet_frames[idx] = temp.tobytes()\n", "\n", "    def stop(self):\n", "        self.running = False\n", "        for cap in self.cap_devices:\n", "            if cap is not None:\n", "                cap.release()\n", "        for thread in self.threads:\n", "            thread.join()\n", "\n", "    def check_PPE(self, objects, poses, base_threshold=50, scale_factor=0.2):\n", "        helmet_id, gloves_id, vest_id = 2, 1, 5\n", "        person_compliance = []  \n", "\n", "        for pose in poses:\n", "            # print(pose)\n", "            keypoints = pose[\"keypoints\"]\n", "            x1, y1, x2, y2 = pose[\"bbox\"]  \n", "            person_height = y2 - y1  # Calculate the height of the person\n", "\n", "            # **Adaptive threshold scaling**\n", "            distance_threshold = max(base_threshold, int(person_height * scale_factor))\n", "\n", "            head_points = np.array([keypoints[1],keypoints[2],keypoints[3],keypoints[4]])\n", "            chest_points = np.array([keypoints[5],keypoints[6],keypoints[11],keypoints[12]])  \n", "            hand_points = np.array([keypoints[9],keypoints[10],])\n", "\n", "            # print(head_points)\n", "            # print(\"----------------\")\n", "            # print(chest_points)\n", "            # print(\"------------\")\n", "            # print(hand_points)\n", "\n", "            helmet_worn, vest_worn, gloves_worn = False, False, False\n", "\n", "            for obj in objects:\n", "                ox1, oy1, ox2, oy2 = obj[\"bbox\"]\n", "                center_x, center_y = (ox1 + ox2) // 2, (oy1 + oy2) // 2 \n", "\n", "                if obj[\"class_id\"] == helmet_id and not helmet_worn and head_points.size > 0:\n", "                    distances = np.linalg.norm(head_points - np.array([center_x, center_y]), axis=1)\n", "                    if np.any(distances < distance_threshold):\n", "                        helmet_worn = True\n", "\n", "                if obj[\"class_id\"] == vest_id and not vest_worn and chest_points.size > 0:\n", "                    distances = np.linalg.norm(chest_points - np.array([center_x, center_y]), axis=1)\n", "\n", "                    if np.any(distances < distance_threshold + 50):  \n", "                        vest_worn = True\n", "\n", "                if obj[\"class_id\"] == gloves_id and not gloves_worn and hand_points.size > 0:\n", "                    distances = np.linalg.norm(hand_points - np.array([center_x, center_y]), axis=1)\n", "                    # self.frame = cv2.circle(self.frame, (center_x, center_y), 5, (0, 255, 0), -1)\n", "                    print(hand_points)\n", "                    # print(\"distances_threshold\",distance_threshold)\n", "                    # print(\"gloves distances\",distances)\n", "                    if np.any(distances < distance_threshold +35):\n", "                        gloves_worn = True\n", "\n", "                if helmet_worn and vest_worn and gloves_worn:\n", "                    break\n", "\n", "            compliance_status = {\n", "                \"bbox\": pose[\"bbox\"],\n", "                \"helmet\": helmet_worn,\n", "                \"vest\": vest_worn,\n", "                \"gloves\": gloves_worn\n", "            }\n", "            person_compliance.append(compliance_status)\n", "            # print(person_compliance)\n", "\n", "        return person_compliance\n", "\n", "\n", "    def process_frame(self, frame):\n", "        # object_results = self.object_model(frame, stream=True,classes = [1,2,9])\n", "        \n", "        # result_generator = self.inferencer(frame,show = False, return_vis = False)\n", "\n", "        with concurrent.futures.ThreadPoolExecutor() as executor:\n", "            future_objects = executor.submit(self.object_model, frame, classes = [1,2,3,5],verbose=False)\n", "            future_poses = executor.submit(self.pose_model, frame,verbose=False)\n", "            \n", "            object_results = future_objects.result()\n", "            pose_results = future_poses.result()\n", "            # print(\"object_results\",object_results)\n", "            # print(\"-----------------------------------------------------\")\n", "            # print(\"pose_results\",pose_results)\n", " \n", "        objects, poses = [], []\n", "\n", "        for object_result in object_results:\n", "            for det in object_result.boxes:\n", "                if det.conf > self.conf_value:\n", "                    x1, y1, x2, y2 = map(int, det.xyxy[0])\n", "                    objects.append({\"class_id\": int(det.cls), \"bbox\": (x1, y1, x2, y2)})\n", "\n", "\n", "\n", "                  \n", "        # Process pose detections (if keypoints exist)\n", "        for pose_result in pose_results:\n", "            if pose_result.keypoints is not None:\n", "                for person, box in zip(pose_result.keypoints, pose_result.boxes):\n", "                    if box.conf > self.pose_conf_value:\n", "                        x1, y1, x2, y2 = map(int, box.xyxy[0])\n", "                        poses.append({\"keypoints\": person.xy.cpu().numpy()[0], \"bbox\": (x1, y1, x2, y2)})\n", "        # print(len(poses))\n", "        \n", "\n", "        return objects, poses\n", "\n", "    def update(self, idx, cap, test_video_path=None):\n", "        frame_counter, skip_frames = 0, 2\n", "        while self.running:\n", "            ret, self.frame = cap.read()\n", "            \n", "            if not ret and test_video_path:\n", "                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)\n", "                continue\n", "            elif not ret:\n", "                break\n", "\n", "            frame_counter += 1\n", "            if frame_counter % skip_frames != 0:\n", "                continue\n", "\n", "            self.frame = cv2.resize(self.frame, (640, 480),interpolation=cv2.INTER_AREA)\n", "\n", "            objects, poses = self.process_frame(self.frame)\n", "\n", "            compliance_results = self.check_PPE(objects=objects, poses=poses)\n", "\n", "\n", "            # Store counts\n", "            self.person_count = len(compliance_results)\n", "            self.no_helmet_count = sum(1 for person in compliance_results if not person[\"helmet\"])\n", "            self.no_vest_count = sum(1 for person in compliance_results if not person[\"vest\"])\n", "            self.no_gloves_count = sum(1 for person in compliance_results if not person[\"gloves\"])\n", "            \n", "            with self.locks[idx]:\n", "                self.warning_count[idx] = {\n", "                    \"person_count\": self.person_count,\n", "                    \"no_helmet_count\": self.no_helmet_count,\n", "                    \"no_vest_count\": self.no_vest_count,\n", "                    \"no_gloves_count\": self.no_gloves_count\n", "                }\n", "\n", "            # print(f\"Total Persons: {self.person_count}, No Helmet: {self.no_helmet_count}, No Vest: {self.no_vest_count}, No Gloves: {self.no_gloves_count}\")\n", "\n", "            # print(compliance_results)\n", "\n", "            # # Draw compliance status on frame\n", "            # for result in compliance_results:\n", "            #     # print(result['bbox'])\n", "            #     x1, y1, x2, y2 = map(int, result[\"bbox\"])\n", "            #     label = []\n", "            #     if result[\"helmet\"]:\n", "            #         label.append(\"Helmet: weared\")\n", "            #     else:\n", "            #         label.append(\"Helmet: not weared\")\n", "\n", "            #     if result[\"vest\"]:\n", "            #         label.append(\"Vest: weared\")\n", "            #     else:\n", "            #         label.append(\"Vest: not weared\")\n", "\n", "            #     if result[\"gloves\"]:\n", "            #         label.append(\"Gloves: weared\")\n", "            #     else:\n", "            #         label.append(\"Gloves: not weared\")\n", "\n", "            #     text = \" | \".join(label)\n", "            #     color = (0, 255, 0) if all([result[\"helmet\"], result[\"vest\"], result[\"gloves\"]]) else (0, 0, 255)\n", "                \n", "            #     cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)\n", "            #     cv2.putText(frame, text, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)\n", "\n", "            # cv2.imshow(\"frame\", frame)\n", "            # cv2.<PERSON><PERSON><PERSON>(1)\n", "            # self.frame = cv2.resize(self.frame, (640, 640),interpolation=cv2.INTER_AREA)\n", "\n", "            _, buffer = cv2.imencode(\".png\", self.frame)\n", "            with self.locks[idx]:\n", "                self.helmet_frames[idx] = buffer.tobytes()\n", "\n", "            if not self.running:\n", "                break\n", "\n", "        # cv2.destroyAllWindows()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from email.mime.text import MIMEText\n", "from email.mime.multipart import MIMEMultipart\n", "import smtplib\n", "import ssl"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sending email alert...\n"]}], "source": ["\n", "# Email settings - should be configured in production\n", "# This is just a basic implementation\n", "smtp_server = \"smtp.gmail.com\"\n", "port = 587\n", "sender_email = \"<EMAIL>\"  # Update this\n", "password = \"lljb owqm pyrj pwnq\"  # Update this - Use app password for Gmail\n", "\n", "# Create message\n", "message = MIMEMultipart(\"alternative\")\n", "message[\"Subject\"] = f\"Alert: People Detected - Front Camera\"\n", "message[\"From\"] = sender_email\n", "\n", "# Email content\n", "html = f\"\"\"\n", "<html>\n", "    <body>\n", "    <h2>Crowd Detection Alert</h2>\n", "    <p>This is an automated alert from your Crowd Detection System.</p>\n", "    <p><strong><PERSON><PERSON>:</strong></p>\n", "    <ul>\n", "        <li><strong>Camera:</strong> front camera</li>\n", "        <li><strong>Region:</strong> region </li>\n", "        <li><strong>People Count:</strong> 10</li>\n", "        <li><strong>Time:</strong> 15:45</li>\n", "    </ul>\n", "    <p>Please check your monitoring system for more details.</p>\n", "    </body>\n", "</html>\n", "\"\"\"\n", "\n", "# Attach HTML content\n", "part = MIMEText(html, \"html\")\n", "message.attach(part)\n", "\n", "# Create a secure SSL context\n", "context = ssl.create_default_context()\n", "recipient = \"<EMAIL>\"\n", "# Connect to server\n", "print(\"Sending email alert...\")\n", "with smtplib.SMTP(smtp_server, port) as server:\n", "    server.starttls(context=context)\n", "    server.login(sender_email, password)\n", "    message[\"To\"] = recipient\n", "    server.sendmail(sender_email, recipient, message.as_string())\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-08 11:57:29.208928\n", "starttime < now < stoptime\n"]}], "source": ["import time\n", "import datetime\n", "\n", "print(datetime.datetime.now())\n", "starttime = datetime.datetime.now()\n", "\n", "time.sleep(10)\n", "now = datetime.datetime.now()\n", "\n", "time.sleep(10)\n", "\n", "stoptime = datetime.datetime.now()\n", "\n", "if starttime < now < stoptime:\n", "    print(\"starttime < now < stoptime\")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-08 11:57:29.208928\n", "<class 'datetime.datetime'>\n"]}], "source": ["from datetime import datetime\n", "\n", "dt_string = \"2025-04-08 11:57:29.208928\"\n", "dt_object = datetime.strptime(dt_string, \"%Y-%m-%d %H:%M:%S.%f\")\n", "\n", "print(dt_object)\n", "print(type(dt_object))\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import smtplib\n", "from email.message import EmailMessage"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["msg = EmailMessage()\n", "msg.set_content(\"hai surya\")\n", "msg[\"Subject\"] = \"Test Email\"\n", "msg[\"From\"] = \"<EMAIL>\"\n", "msg[\"To\"] = \"<EMAIL>\"\n", "\n", "with smtplib.SMTP_SSL(\"smtp.gmail.com\", 465) as smtp:\n", "    smtp.login(\"<EMAIL>\", \"lljb owqm pyrj pwnq\")\n", "    smtp.send_message(msg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["deepsort"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import threading\n", "from ultralytics import YOLO\n", "import numpy as np\n", "import concurrent.futures\n", "from deep_sort_realtime.deepsort_tracker import DeepSort\n", "\n", "class Helmetdetection:\n", "    def __init__(self, object_model, pose_model, conf_value):\n", "        self.conf_value = conf_value\n", "        self.object_model = YOLO(object_model)  \n", "        self.pose_model = YOLO(pose_model)\n", "        self.threads = []\n", "        self.running = False\n", "        self.blank = cv2.imread(\"./static/black.jpg\")\n", "        self.blank = cv2.resize(self.blank, (640, 640), interpolation=cv2.INTER_AREA)\n", "        \n", "        # Initialize separate DeepSORT trackers for persons and each PPE type\n", "        self.person_tracker = DeepSort(\n", "            max_age=30, n_init=3, nms_max_overlap=1.0,\n", "            max_cosine_distance=0.3, nn_budget=None,\n", "            embedder=\"mobilenet\", half=True, bgr=True, embedder_gpu=True\n", "        )\n", "        \n", "        self.helmet_tracker = DeepSort(\n", "            max_age=20, n_init=2, nms_max_overlap=0.9,\n", "            max_cosine_distance=0.4, nn_budget=None,\n", "            embedder=\"mobilenet\", half=True, bgr=True, embedder_gpu=True\n", "        )\n", "        \n", "        self.vest_tracker = DeepSort(\n", "            max_age=20, n_init=2, nms_max_overlap=0.9,\n", "            max_cosine_distance=0.4, nn_budget=None,\n", "            embedder=\"mobilenet\", half=True, bgr=True, embedder_gpu=True\n", "        )\n", "        \n", "        self.gloves_tracker = DeepSort(\n", "            max_age=15, n_init=2, nms_max_overlap=0.9,\n", "            max_cosine_distance=0.4, nn_budget=None,\n", "            embedder=\"mobilenet\", half=True, bgr=True, embedder_gpu=True\n", "        )\n", "        \n", "        # Class IDs for each PPE type\n", "        self.HELMET_CLASS_ID = 2\n", "        self.GLOVES_CLASS_ID = 1\n", "        self.VEST_CLASS_ID = 5\n", "\n", "    def start(self, camera_details, test_video_path=None):\n", "        self.person_count, self.no_helmet_count, self.no_vest_count, self.no_gloves_count = 0, 0, 0, 0\n", "        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []\n", "        dic = camera_details\n", "\n", "        # If test_video_path is provided, override camera details\n", "        if test_video_path:\n", "            self.camera_name.append(\"Test Video\")\n", "            cap = cv2.VideoCapture(test_video_path)\n", "            if cap.isOpened():\n", "                self.cap_devices.append(cap)\n", "            else:\n", "                self.cap_devices.append(None)\n", "        else:\n", "            for key, value in dic.items():\n", "                self.camera_name.append(key)\n", "                if value[0].isdigit():\n", "                    value = int(value[0])\n", "                    self.rtsp_url.append(value)\n", "                    cap = cv2.VideoCapture(value)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "                else:\n", "                    cap = cv2.VideoCapture(value[0], cv2.CAP_FFMPEG)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "\n", "        self.helmet_frames = [None] * len(self.cap_devices)\n", "        self.warning_count = [{\"person_count\": 0, \"no_helmet_count\": 0, \"no_vest_count\": 0, \"no_gloves_count\": 0} for _ in range(len(self.cap_devices))]\n", "\n", "        if not self.running:\n", "            self.running = True\n", "            for idx, cap in enumerate(self.cap_devices):\n", "                if cap is not None and cap.isOpened():\n", "                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))\n", "                    thread.daemon = True\n", "                    thread.start()\n", "                    self.threads.append(thread)\n", "                else:\n", "                    temp = cv2.putText(\n", "                        self.blank,\n", "                        f\"{self.camera_name[idx]} is Offline\",\n", "                        (35, 170),\n", "                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,\n", "                        fontScale=1,\n", "                        thickness=3,\n", "                        color=(255, 255, 255),\n", "                    )\n", "                    _, temp = cv2.imencode(\".png\", temp)\n", "                    self.helmet_frames[idx] = temp.tobytes()\n", "\n", "    def stop(self):\n", "        self.running = False\n", "        for cap in self.cap_devices:\n", "            if cap is not None:\n", "                cap.release()\n", "        for thread in self.threads:\n", "            thread.join()\n", "\n", "    def check_PPE(self, tracked_helmets, tracked_vests, tracked_gloves, poses, tracked_persons, base_threshold=50, scale_factor=0.2):\n", "        person_compliance = []  \n", "\n", "        for pose, tracked_person in zip(poses, tracked_persons):\n", "            keypoints = pose[\"keypoints\"]\n", "            x1, y1, x2, y2 = pose[\"bbox\"]  \n", "            person_height = y2 - y1\n", "            track_id = tracked_person[\"track_id\"]\n", "\n", "            # Adaptive threshold scaling\n", "            distance_threshold = max(base_threshold, int(person_height * scale_factor))\n", "\n", "            head_points = np.array([keypoints[1], keypoints[2], keypoints[3], keypoints[4]])\n", "            chest_points = np.array([keypoints[5], keypoints[6], keypoints[11], keypoints[12]])\n", "            hand_points = np.array([keypoints[9], keypoints[10]])\n", "\n", "            # Initialize with no PPE\n", "            helmet_worn, vest_worn, gloves_worn = False, False, False\n", "            helmet_id, vest_id, gloves_id = None, None, None\n", "\n", "            # Check for helmets near head\n", "            for helmet in tracked_helmets:\n", "                hx1, hy1, hx2, hy2 = helmet[\"bbox\"]\n", "                helmet_center = [(hx1 + hx2) // 2, (hy1 + hy2) // 2]\n", "                \n", "                if head_points.size > 0:\n", "                    distances = np.linalg.norm(head_points - np.array(helmet_center), axis=1)\n", "                    if np.any(distances < distance_threshold):\n", "                        helmet_worn = True\n", "                        helmet_id = helmet[\"track_id\"]\n", "                        break\n", "\n", "            # Check for vests near chest\n", "            for vest in tracked_vests:\n", "                vx1, vy1, vx2, vy2 = vest[\"bbox\"]\n", "                vest_center = [(vx1 + vx2) // 2, (vy1 + vy2) // 2]\n", "                \n", "                if chest_points.size > 0:\n", "                    distances = np.linalg.norm(chest_points - np.array(vest_center), axis=1)\n", "                    if np.any(distances < distance_threshold + 50):\n", "                        vest_worn = True\n", "                        vest_id = vest[\"track_id\"]\n", "                        break\n", "\n", "            # Check for gloves near hands\n", "            for glove in tracked_gloves:\n", "                gx1, gy1, gx2, gy2 = glove[\"bbox\"]\n", "                glove_center = [(gx1 + gx2) // 2, (gy1 + gy2) // 2]\n", "                \n", "                if hand_points.size > 0:\n", "                    distances = np.linalg.norm(hand_points - np.array(glove_center), axis=1)\n", "                    if np.any(distances < distance_threshold + 35):\n", "                        gloves_worn = True\n", "                        gloves_id = glove[\"track_id\"]\n", "                        break\n", "\n", "            compliance_status = {\n", "                \"bbox\": pose[\"bbox\"],\n", "                \"person_id\": track_id,\n", "                \"helmet\": helmet_worn,\n", "                \"helmet_id\": helmet_id,\n", "                \"vest\": vest_worn,\n", "                \"vest_id\": vest_id,\n", "                \"gloves\": gloves_worn,\n", "                \"gloves_id\": gloves_id\n", "            }\n", "            person_compliance.append(compliance_status)\n", "\n", "        return person_compliance\n", "\n", "    def process_frame(self, frame):\n", "        # Run object and pose detection\n", "        with concurrent.futures.ThreadPoolExecutor() as executor:\n", "            future_objects = executor.submit(self.object_model, frame)\n", "            future_poses = executor.submit(self.pose_model, frame)\n", "            \n", "            object_results = future_objects.result()\n", "            pose_results = future_poses.result()\n", " \n", "        # Initialize containers for detections\n", "        person_detections = []\n", "        helmet_detections = []\n", "        vest_detections = []\n", "        gloves_detections = []\n", "        poses = []\n", "\n", "        # Process object detections by type\n", "        for object_result in object_results:\n", "            for det in object_result.boxes:\n", "                if det.conf > self.conf_value:\n", "                    x1, y1, x2, y2 = map(int, det.xyxy[0])\n", "                    class_id = int(det.cls)\n", "                    detection = [x1, y1, x2-x1, y2-y1]  # Format: [x, y, width, height]\n", "                    \n", "                    # Sort detections by class\n", "                    if class_id == self.HELMET_CLASS_ID:\n", "                        helmet_detections.append((detection, det.conf.item(), \"helmet\"))\n", "                    elif class_id == self.VEST_CLASS_ID:\n", "                        vest_detections.append((detection, det.conf.item(), \"vest\"))\n", "                    elif class_id == self.GLOVES_CLASS_ID:\n", "                        gloves_detections.append((detection, det.conf.item(), \"gloves\"))\n", "                  \n", "        # Process pose detections for people\n", "        for pose_result in pose_results:\n", "            if pose_result.keypoints is not None:\n", "                for person, box in zip(pose_result.keypoints, pose_result.boxes):\n", "                    if box.conf > self.conf_value:\n", "                        x1, y1, x2, y2 = map(int, box.xyxy[0])\n", "                        \n", "                        # Save pose information\n", "                        poses.append({\n", "                            \"keypoints\": person.xy.cpu().numpy()[0], \n", "                            \"bbox\": (x1, y1, x2, y2)\n", "                        })\n", "                        \n", "                        # Create detection for person tracker\n", "                        detection = [x1, y1, x2 - x1, y2 - y1]\n", "                        person_detections.append((detection, box.conf.item(), \"person\"))\n", "\n", "        # Update trackers with new detections\n", "        tracked_persons = self._update_tracker(self.person_tracker, person_detections, frame)\n", "        tracked_helmets = self._update_tracker(self.helmet_tracker, helmet_detections, frame)\n", "        tracked_vests = self._update_tracker(self.vest_tracker, vest_detections, frame)\n", "        tracked_gloves = self._update_tracker(self.gloves_tracker, gloves_detections, frame)\n", "\n", "        # Ensure poses and tracked_persons are aligned\n", "        if len(poses) > len(tracked_persons):\n", "            poses = poses[:len(tracked_persons)]\n", "        elif len(poses) < len(tracked_persons):\n", "            tracked_persons = tracked_persons[:len(poses)]\n", "        \n", "        return poses, tracked_persons, tracked_helmets, tracked_vests, tracked_gloves\n", "\n", "    def _update_tracker(self, tracker, detections, frame):\n", "        \"\"\"Helper method to update any tracker and return formatted results\"\"\"\n", "        tracked_objects = []\n", "        if detections:\n", "            tracks = tracker.update_tracks(detections, frame=frame)\n", "            \n", "            for track in tracks:\n", "                if track.is_confirmed():\n", "                    track_id = track.track_id\n", "                    ltrb = track.to_ltrb()\n", "                    tracked_objects.append({\n", "                        \"track_id\": track_id,\n", "                        \"bbox\": (int(ltrb[0]), int(ltrb[1]), int(ltrb[2]), int(ltrb[3]))\n", "                    })\n", "        \n", "        return tracked_objects\n", "\n", "    def update(self, idx, cap, test_video_path=None):\n", "        frame_counter, skip_frames = 0, 2\n", "        while self.running:\n", "            ret, self.frame = cap.read()\n", "            \n", "            if not ret and test_video_path:\n", "                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)\n", "                continue\n", "            elif not ret:\n", "                break\n", "\n", "            frame_counter += 1\n", "            if frame_counter % skip_frames != 0:\n", "                continue\n", "\n", "            self.frame = cv2.resize(self.frame, (640, 480), interpolation=cv2.INTER_AREA)\n", "\n", "            # Process current frame\n", "            poses, tracked_persons, tracked_helmets, tracked_vests, tracked_gloves = self.process_frame(self.frame)\n", "            compliance_results = []\n", "            # If we have any people to track\n", "            if poses and tracked_persons:\n", "                compliance_results = self.check_PPE(\n", "                    tracked_helmets=tracked_helmets,\n", "                    tracked_vests=tracked_vests,\n", "                    tracked_gloves=tracked_gloves,\n", "                    poses=poses,\n", "                    tracked_persons=tracked_persons\n", "                )\n", "\n", "                # Store counts\n", "                self.person_count = len(compliance_results)\n", "                self.no_helmet_count = sum(1 for person in compliance_results if not person[\"helmet\"])\n", "                self.no_vest_count = sum(1 for person in compliance_results if not person[\"vest\"])\n", "                self.no_gloves_count = sum(1 for person in compliance_results if not person[\"gloves\"])\n", "\n", "                self.warning_count[idx] = {\n", "                    \"person_count\": self.person_count,\n", "                    \"no_helmet_count\": self.no_helmet_count,\n", "                    \"no_vest_count\": self.no_vest_count,\n", "                    \"no_gloves_count\": self.no_gloves_count\n", "                }\n", "\n", "                # Draw compliance status, PPE IDs, and person IDs on frame\n", "                self._draw_annotations(compliance_results, tracked_helmets, tracked_vests, tracked_gloves)\n", "\n", "            # Draw PPE items not associated with any person\n", "            self._draw_unassociated_ppe(tracked_helmets, tracked_vests, tracked_gloves, compliance_results)\n", "\n", "            _, buffer = cv2.imencode(\".png\", self.frame)\n", "            self.helmet_frames[idx] = buffer.tobytes()\n", "\n", "            if not self.running:\n", "                break\n", "\n", "    def _draw_annotations(self, compliance_results, tracked_helmets, tracked_vests, tracked_gloves):\n", "        \"\"\"Draw all person compliance and PPE status on the frame\"\"\"\n", "        # Draw person annotations\n", "        for result in compliance_results:\n", "            x1, y1, x2, y2 = map(int, result[\"bbox\"])\n", "            person_id = result[\"person_id\"]\n", "            \n", "            # Determine person compliance status\n", "            is_compliant = all([result[\"helmet\"], result[\"vest\"], result[\"gloves\"]])\n", "            person_color = (0, 255, 0) if is_compliant else (0, 0, 255)\n", "            \n", "            # Draw person bounding box\n", "            cv2.rectangle(self.frame, (x1, y1), (x2, y2), person_color, 2)\n", "            \n", "            # Draw person ID\n", "            cv2.putText(self.frame, f\"Person #{person_id}\", (x1, y1 - 10), \n", "                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)\n", "            \n", "            # Draw PPE status with IDs\n", "            y_offset = 20\n", "            \n", "            # Helmet status\n", "            helmet_text = f\"Helmet: Yes (#{result['helmet_id']})\" if result[\"helmet\"] else \"Helmet: No\"\n", "            cv2.putText(self.frame, helmet_text, (x1, y1 + y_offset), \n", "                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0) if result[\"helmet\"] else (0, 0, 255), 2)\n", "            \n", "            # Vest status\n", "            y_offset += 20\n", "            vest_text = f\"Vest: Yes (#{result['vest_id']})\" if result[\"vest\"] else \"Vest: No\"\n", "            cv2.putText(self.frame, vest_text, (x1, y1 + y_offset), \n", "                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0) if result[\"vest\"] else (0, 0, 255), 2)\n", "            \n", "            # Gloves status\n", "            y_offset += 20\n", "            gloves_text = f\"Gloves: Yes (#{result['gloves_id']})\" if result[\"gloves\"] else \"Gloves: No\"\n", "            cv2.putText(self.frame, gloves_text, (x1, y1 + y_offset), \n", "                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0) if result[\"gloves\"] else (0, 0, 255), 2)\n", "\n", "    def _draw_unassociated_ppe(self, tracked_helmets, tracked_vests, tracked_gloves, compliance_results):\n", "        \"\"\"Draw PPE items that are not associated with any person\"\"\"\n", "        # Create sets of PPE IDs that are already associated with people\n", "        used_helmet_ids = {result[\"helmet_id\"] for result in compliance_results if result[\"helmet_id\"] is not None}\n", "        used_vest_ids = {result[\"vest_id\"] for result in compliance_results if result[\"vest_id\"] is not None}\n", "        used_gloves_ids = {result[\"gloves_id\"] for result in compliance_results if result[\"gloves_id\"] is not None}\n", "        \n", "        # Draw unassociated helmets\n", "        for helmet in tracked_helmets:\n", "            if helmet[\"track_id\"] not in used_helmet_ids:\n", "                x1, y1, x2, y2 = helmet[\"bbox\"]\n", "                cv2.rectangle(self.frame, (x1, y1), (x2, y2), (255, 0, 0), 2)\n", "                cv2.putText(self.frame, f\"Helmet #{helmet['track_id']}\", (x1, y1 - 5), \n", "                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)\n", "        \n", "        # Draw unassociated vests\n", "        for vest in tracked_vests:\n", "            if vest[\"track_id\"] not in used_vest_ids:\n", "                x1, y1, x2, y2 = vest[\"bbox\"]\n", "                cv2.rectangle(self.frame, (x1, y1), (x2, y2), (0, 255, 255), 2)\n", "                cv2.putText(self.frame, f\"Vest #{vest['track_id']}\", (x1, y1 - 5), \n", "                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)\n", "        \n", "        # Draw unassociated gloves\n", "        for glove in tracked_gloves:\n", "            if glove[\"track_id\"] not in used_gloves_ids:\n", "                x1, y1, x2, y2 = glove[\"bbox\"]\n", "                cv2.rectangle(self.frame, (x1, y1), (x2, y2), (128, 0, 128), 2)\n", "                cv2.putText(self.frame, f\"Gloves #{glove['track_id']}\", (x1, y1 - 5), \n", "                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 0, 128), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["without tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import threading\n", "from ultralytics import YOLO\n", "import numpy as np\n", "import concurrent.futures\n", "\n", "class Helmetdetection:\n", "    def __init__(self, object_model,pose_model,conf_value):\n", "        self.conf_value = conf_value\n", "        self.object_model = YOLO(object_model)  \n", "        self.pose_model = YOLO(pose_model)\n", "        self.threads = []\n", "        self.running = False\n", "        self.blank = cv2.imread(\"./static/black.jpg\")\n", "        self.blank = cv2.resize(self.blank, (640, 640),interpolation=cv2.INTER_AREA)\n", "\n", "\n", "    def start(self, camera_details, test_video_path=None):\n", "        self.person_count,self.no_helmet_count,self.no_vest_count,self.no_gloves_count = 0,0,0,0\n", "        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []\n", "        dic = camera_details\n", "\n", "        # If test_video_path is provided, override camera details\n", "        if test_video_path:\n", "            self.camera_name.append(\"Test Video\")\n", "            cap = cv2.VideoCapture(test_video_path)\n", "            if cap.isOpened():\n", "                self.cap_devices.append(cap)\n", "            else:\n", "                self.cap_devices.append(None)\n", "        else:\n", "            for key, value in dic.items():\n", "                self.camera_name.append(key)\n", "                if value[0].isdigit():\n", "                    value = int(value[0])\n", "                    self.rtsp_url.append(value)\n", "                    cap = cv2.VideoCapture(value)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "                else:\n", "                    cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "\n", "        self.helmet_frames = [None] * len(self.cap_devices)\n", "        self.warning_count = [{\"person_count\": 0, \"no_helmet_count\": 0, \"no_vest_count\": 0, \"no_gloves_count\": 0} for _ in range(len(self.cap_devices))]\n", "\n", "        if not self.running:\n", "            self.running = True\n", "            for idx, cap in enumerate(self.cap_devices):\n", "                if cap is not None and cap.isOpened():\n", "                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))\n", "                    thread.daemon = True\n", "                    thread.start()\n", "                    self.threads.append(thread)\n", "                else:\n", "                    temp = cv2.putText(\n", "                        self.blank,\n", "                        f\"{self.camera_name[idx]} is Offline\",\n", "                        (35, 170),\n", "                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,\n", "                        fontScale=1,\n", "                        thickness=3,\n", "                        color=(255, 255, 255),\n", "                    )\n", "                    _, temp = cv2.imencode(\".png\", temp)\n", "                    self.helmet_frames[idx] = temp.tobytes()\n", "\n", "    def stop(self):\n", "        self.running = False\n", "        for cap in self.cap_devices:\n", "            if cap is not None:\n", "                cap.release()\n", "        for thread in self.threads:\n", "            thread.join()\n", "\n", "    def check_PPE(self, objects, poses, base_threshold=50, scale_factor=0.2):\n", "        helmet_id, gloves_id, vest_id = 2, 1, 5\n", "        person_compliance = []  \n", "\n", "        for pose in poses:\n", "            # print(pose)\n", "            keypoints = pose[\"keypoints\"]\n", "            x1, y1, x2, y2 = pose[\"bbox\"]  \n", "            person_height = y2 - y1  # Calculate the height of the person\n", "\n", "            # **Adaptive threshold scaling**\n", "            distance_threshold = max(base_threshold, int(person_height * scale_factor))\n", "\n", "            head_points = np.array([keypoints[1],keypoints[2],keypoints[3],keypoints[4]])\n", "            chest_points = np.array([keypoints[5],keypoints[6],keypoints[11],keypoints[12]])   #area for improvement\n", "            hand_points = np.array([keypoints[9],keypoints[10],])\n", "\n", "            # print(head_points)\n", "            # print(\"----------------\")\n", "            # print(chest_points)\n", "            # print(\"------------\")\n", "            # print(hand_points)\n", "\n", "            helmet_worn, vest_worn, gloves_worn = False, False, False\n", "\n", "            for obj in objects:\n", "                ox1, oy1, ox2, oy2 = obj[\"bbox\"]\n", "                center_x, center_y = (ox1 + ox2) // 2, (oy1 + oy2) // 2 \n", "\n", "                if obj[\"class_id\"] == helmet_id and not helmet_worn and head_points.size > 0:\n", "                    distances = np.linalg.norm(head_points - np.array([center_x, center_y]), axis=1)\n", "                    if np.any(distances < distance_threshold):\n", "                        helmet_worn = True\n", "\n", "                if obj[\"class_id\"] == vest_id and not vest_worn and chest_points.size > 0:\n", "                    distances = np.linalg.norm(chest_points - np.array([center_x, center_y]), axis=1)\n", "\n", "                    if np.any(distances < distance_threshold + 50):  \n", "                        vest_worn = True\n", "\n", "                if obj[\"class_id\"] == gloves_id and not gloves_worn and hand_points.size > 0:\n", "                    distances = np.linalg.norm(hand_points - np.array([center_x, center_y]), axis=1)\n", "                    # self.frame = cv2.circle(self.frame, (center_x, center_y), 5, (0, 255, 0), -1)\n", "                    print(hand_points)\n", "                    # print(\"distances_threshold\",distance_threshold)\n", "                    # print(\"gloves distances\",distances)\n", "                    if np.any(distances < distance_threshold +35):\n", "                        gloves_worn = True\n", "\n", "                if helmet_worn and vest_worn and gloves_worn:\n", "                    break\n", "\n", "            compliance_status = {\n", "                \"bbox\": pose[\"bbox\"],\n", "                \"helmet\": helmet_worn,\n", "                \"vest\": vest_worn,\n", "                \"gloves\": gloves_worn\n", "            }\n", "            person_compliance.append(compliance_status)\n", "            # print(person_compliance)\n", "\n", "        return person_compliance\n", "\n", "\n", "    def process_frame(self, frame):\n", "        # object_results = self.object_model(frame, stream=True,classes = [1,2,9])\n", "        \n", "        # result_generator = self.inferencer(frame,show = False, return_vis = False)\n", "\n", "        with concurrent.futures.ThreadPoolExecutor() as executor:\n", "            future_objects = executor.submit(self.object_model, frame)\n", "            future_poses = executor.submit(self.pose_model, frame)\n", "            \n", "            object_results = future_objects.result()\n", "            pose_results = future_poses.result()\n", "            # print(\"object_results\",object_results)\n", "            # print(\"-----------------------------------------------------\")\n", "            # print(\"pose_results\",pose_results)\n", " \n", "        objects, poses = [], []\n", "\n", "        for object_result in object_results:\n", "            for det in object_result.boxes:\n", "                if det.conf > self.conf_value:\n", "                    x1, y1, x2, y2 = map(int, det.xyxy[0])\n", "                    objects.append({\"class_id\": int(det.cls), \"bbox\": (x1, y1, x2, y2)})\n", "\n", "\n", "\n", "                  \n", "        # Process pose detections (if keypoints exist)\n", "        for pose_result in pose_results:\n", "            if pose_result.keypoints is not None:\n", "                for person, box in zip(pose_result.keypoints, pose_result.boxes):\n", "                    if box.conf > self.conf_value:\n", "                        x1, y1, x2, y2 = map(int, box.xyxy[0])\n", "                        poses.append({\"keypoints\": person.xy.cpu().numpy()[0], \"bbox\": (x1, y1, x2, y2)})\n", "        print(len(poses))\n", "\n", "        return objects, poses\n", "\n", "    def update(self, idx, cap, test_video_path=None):\n", "        frame_counter, skip_frames = 0, 2\n", "        while self.running:\n", "            ret, self.frame = cap.read()\n", "            \n", "            if not ret and test_video_path:\n", "                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)\n", "                continue\n", "            elif not ret:\n", "                break\n", "\n", "            frame_counter += 1\n", "            if frame_counter % skip_frames != 0:\n", "                continue\n", "\n", "            self.frame = cv2.resize(self.frame, (640, 480),interpolation=cv2.INTER_AREA)\n", "\n", "            objects, poses = self.process_frame(self.frame)\n", "\n", "            compliance_results = self.check_PPE(objects=objects, poses=poses)\n", "\n", "\n", "            # Store counts\n", "            self.person_count = len(compliance_results)\n", "            self.no_helmet_count = sum(1 for person in compliance_results if not person[\"helmet\"])\n", "            self.no_vest_count = sum(1 for person in compliance_results if not person[\"vest\"])\n", "            self.no_gloves_count = sum(1 for person in compliance_results if not person[\"gloves\"])\n", "\n", "            self.warning_count[idx] = {\n", "                \"person_count\": self.person_count,\n", "                \"no_helmet_count\": self.no_helmet_count,\n", "                \"no_vest_count\": self.no_vest_count,\n", "                \"no_gloves_count\": self.no_gloves_count\n", "            }\n", "\n", "            # print(f\"Total Persons: {self.person_count}, No Helmet: {self.no_helmet_count}, No Vest: {self.no_vest_count}, No Gloves: {self.no_gloves_count}\")\n", "\n", "            # print(compliance_results)\n", "\n", "            # # Draw compliance status on frame\n", "            # for result in compliance_results:\n", "            #     # print(result['bbox'])\n", "            #     x1, y1, x2, y2 = map(int, result[\"bbox\"])\n", "            #     label = []\n", "            #     if result[\"helmet\"]:\n", "            #         label.append(\"Helmet: weared\")\n", "            #     else:\n", "            #         label.append(\"Helmet: not weared\")\n", "\n", "            #     if result[\"vest\"]:\n", "            #         label.append(\"Vest: weared\")\n", "            #     else:\n", "            #         label.append(\"Vest: not weared\")\n", "\n", "            #     if result[\"gloves\"]:\n", "            #         label.append(\"Gloves: weared\")\n", "            #     else:\n", "            #         label.append(\"Gloves: not weared\")\n", "\n", "            #     text = \" | \".join(label)\n", "            #     color = (0, 255, 0) if all([result[\"helmet\"], result[\"vest\"], result[\"gloves\"]]) else (0, 0, 255)\n", "                \n", "            #     cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)\n", "            #     cv2.putText(frame, text, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)\n", "\n", "            # cv2.imshow(\"frame\", frame)\n", "            # cv2.<PERSON><PERSON><PERSON>(1)\n", "            # self.frame = cv2.resize(self.frame, (640, 640),interpolation=cv2.INTER_AREA)\n", "\n", "            _, buffer = cv2.imencode(\".png\", self.frame)\n", "            self.helmet_frames[idx] = buffer.tobytes()\n", "\n", "            if not self.running:\n", "                break\n", "\n", "        # cv2.destroyAllWindows()\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["bytetrack tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import threading\n", "from ultralytics import YOLO\n", "import numpy as np\n", "import concurrent.futures\n", "\n", "class Helmetdetection:\n", "    def __init__(self, object_model, pose_model, conf_value, tracking=True):\n", "        self.conf_value = conf_value\n", "        self.object_model = YOLO(object_model)  \n", "        self.pose_model = YOLO(pose_model)\n", "        self.threads = []\n", "        self.running = False\n", "        self.tracking_enabled = tracking\n", "        self.blank = cv2.imread(\"./static/black.jpg\")\n", "        self.blank = cv2.resize(self.blank, (640, 640), interpolation=cv2.INTER_AREA)\n", "        \n", "        # Configure tracking parameters\n", "        self.tracking_config = {\n", "            'tracker': 'bytetrack.yaml',  # Most robust YOLO tracking method\n", "            'persist': True,  # Persist tracks between frames\n", "            'conf': self.conf_value,  # Confidence threshold for tracking\n", "        }\n", "\n", "    def start(self, camera_details, test_video_path=None):\n", "        self.person_count,self.no_helmet_count,self.no_vest_count,self.no_gloves_count = 0,0,0,0\n", "        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []\n", "        dic = camera_details\n", "\n", "        # If test_video_path is provided, override camera details\n", "        if test_video_path:\n", "            self.camera_name.append(\"Test Video\")\n", "            cap = cv2.VideoCapture(test_video_path)\n", "            if cap.isOpened():\n", "                self.cap_devices.append(cap)\n", "            else:\n", "                self.cap_devices.append(None)\n", "        else:\n", "            for key, value in dic.items():\n", "                self.camera_name.append(key)\n", "                if value[0].isdigit():\n", "                    value = int(value[0])\n", "                    self.rtsp_url.append(value)\n", "                    cap = cv2.VideoCapture(value)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "                else:\n", "                    cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)\n", "                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)\n", "                    if cap.isOpened():\n", "                        self.cap_devices.append(cap)\n", "                    else:\n", "                        self.cap_devices.append(None)\n", "\n", "        self.helmet_frames = [None] * len(self.cap_devices)\n", "        self.warning_count = [{\"person_count\": 0, \"no_helmet_count\": 0, \"no_vest_count\": 0, \"no_gloves_count\": 0} for _ in range(len(self.cap_devices))]\n", "\n", "        if not self.running:\n", "            self.running = True\n", "            for idx, cap in enumerate(self.cap_devices):\n", "                if cap is not None and cap.isOpened():\n", "                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))\n", "                    thread.daemon = True\n", "                    thread.start()\n", "                    self.threads.append(thread)\n", "                else:\n", "                    temp = cv2.putText(\n", "                        self.blank,\n", "                        f\"{self.camera_name[idx]} is Offline\",\n", "                        (35, 170),\n", "                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,\n", "                        fontScale=1,\n", "                        thickness=3,\n", "                        color=(255, 255, 255),\n", "                    )\n", "                    _, temp = cv2.imencode(\".png\", temp)\n", "                    self.helmet_frames[idx] = temp.tobytes()\n", "\n", "    def stop(self):\n", "        self.running = False\n", "        for cap in self.cap_devices:\n", "            if cap is not None:\n", "                cap.release()\n", "        for thread in self.threads:\n", "            thread.join()\n", "\n", "    def check_PPE(self, objects, poses, base_threshold=50, scale_factor=0.2):\n", "        helmet_id, gloves_id, vest_id = 2, 1, 5\n", "        person_compliance = []  \n", "\n", "        for pose in poses:\n", "            # print(pose)\n", "            keypoints = pose[\"keypoints\"]\n", "            x1, y1, x2, y2 = pose[\"bbox\"]  \n", "            person_height = y2 - y1  # Calculate the height of the person\n", "\n", "            # **Adaptive threshold scaling**\n", "            distance_threshold = max(base_threshold, int(person_height * scale_factor))\n", "\n", "            head_points = np.array([keypoints[1],keypoints[2],keypoints[3],keypoints[4]])\n", "            chest_points = np.array([keypoints[5],keypoints[6],keypoints[11],keypoints[12]])   #area for improvement\n", "            hand_points = np.array([keypoints[9],keypoints[10],])\n", "\n", "            # print(head_points)\n", "            # print(\"----------------\")\n", "            # print(chest_points)\n", "            # print(\"------------\")\n", "            # print(hand_points)\n", "\n", "            helmet_worn, vest_worn, gloves_worn = False, False, False\n", "\n", "            for obj in objects:\n", "                ox1, oy1, ox2, oy2 = obj[\"bbox\"]\n", "                center_x, center_y = (ox1 + ox2) // 2, (oy1 + oy2) // 2 \n", "\n", "                if obj[\"class_id\"] == helmet_id and not helmet_worn and head_points.size > 0:\n", "                    distances = np.linalg.norm(head_points - np.array([center_x, center_y]), axis=1)\n", "                    if np.any(distances < distance_threshold):\n", "                        helmet_worn = True\n", "\n", "                if obj[\"class_id\"] == vest_id and not vest_worn and chest_points.size > 0:\n", "                    distances = np.linalg.norm(chest_points - np.array([center_x, center_y]), axis=1)\n", "\n", "                    if np.any(distances < distance_threshold + 50):  \n", "                        vest_worn = True\n", "\n", "                if obj[\"class_id\"] == gloves_id and not gloves_worn and hand_points.size > 0:\n", "                    distances = np.linalg.norm(hand_points - np.array([center_x, center_y]), axis=1)\n", "                    # self.frame = cv2.circle(self.frame, (center_x, center_y), 5, (0, 255, 0), -1)\n", "                    print(hand_points)\n", "                    # print(\"distances_threshold\",distance_threshold)\n", "                    # print(\"gloves distances\",distances)\n", "                    if np.any(distances < distance_threshold +35):\n", "                        gloves_worn = True\n", "\n", "                if helmet_worn and vest_worn and gloves_worn:\n", "                    break\n", "\n", "            compliance_status = {\n", "                \"bbox\": pose[\"bbox\"],\n", "                \"helmet\": helmet_worn,\n", "                \"vest\": vest_worn,\n", "                \"gloves\": gloves_worn\n", "            }\n", "            person_compliance.append(compliance_status)\n", "            # print(person_compliance)\n", "\n", "        return person_compliance\n", "\n", "\n", "    def process_frame(self, frame):\n", "        with concurrent.futures.ThreadPoolExecutor() as executor:\n", "            # Use tracking if enabled\n", "            if self.tracking_enabled:\n", "                future_objects = executor.submit(self.object_model.track, frame, **self.tracking_config)\n", "                future_poses = executor.submit(self.pose_model.track, frame, **self.tracking_config)\n", "            else:\n", "                future_objects = executor.submit(self.object_model, frame)\n", "                future_poses = executor.submit(self.pose_model, frame)\n", "            \n", "            object_results = future_objects.result()\n", "            pose_results = future_poses.result()\n", " \n", "        objects, poses = [], []\n", "\n", "        for object_result in object_results:\n", "            for det in object_result.boxes:\n", "                # Check if tracking is enabled and object has a track ID\n", "                if det.conf > self.conf_value:\n", "                    x1, y1, x2, y2 = map(int, det.xyxy[0])\n", "                    \n", "                    # Include track ID if available\n", "                    track_id = int(det.id[0]) if hasattr(det, 'id') and det.id is not None else None\n", "                    # print(track_id)\n", "                    \n", "                    obj_data = {\n", "                        \"class_id\": int(det.cls), \n", "                        \"bbox\": (x1, y1, x2, y2),\n", "                        \"track_id\": track_id\n", "                    }\n", "                    objects.append(obj_data)\n", "\n", "        # Process pose detections (if keypoints exist)\n", "        for pose_result in pose_results:\n", "            if pose_result.keypoints is not None:\n", "                for person, box in zip(pose_result.keypoints, pose_result.boxes):\n", "                    if box.conf > self.conf_value:\n", "                        x1, y1, x2, y2 = map(int, box.xyxy[0])\n", "                        \n", "                        # Include track ID for poses as well\n", "                        track_id = int(box.id[0]) if hasattr(box, 'id') and box.id is not None else None\n", "                        \n", "                        pose_data = {\n", "                            \"keypoints\": person.xy.cpu().numpy()[0], \n", "                            \"bbox\": (x1, y1, x2, y2),\n", "                            \"track_id\": track_id\n", "                        }\n", "                        poses.append(pose_data)\n", "\n", "        return objects, poses\n", "    \n", "    def update(self, idx, cap, test_video_path=None):\n", "        frame_counter, skip_frames = 0, 2\n", "        while self.running:\n", "            ret, self.frame = cap.read()\n", "            \n", "            if not ret and test_video_path:\n", "                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)\n", "                continue\n", "            elif not ret:\n", "                break\n", "\n", "            frame_counter += 1\n", "            if frame_counter % skip_frames != 0:\n", "                continue\n", "\n", "            self.frame = cv2.resize(self.frame, (640, 480),interpolation=cv2.INTER_AREA)\n", "\n", "            objects, poses = self.process_frame(self.frame)\n", "\n", "            compliance_results = self.check_PPE(objects=objects, poses=poses)\n", "\n", "\n", "            # Store counts\n", "            self.person_count = len(compliance_results)\n", "            self.no_helmet_count = sum(1 for person in compliance_results if not person[\"helmet\"])\n", "            self.no_vest_count = sum(1 for person in compliance_results if not person[\"vest\"])\n", "            self.no_gloves_count = sum(1 for person in compliance_results if not person[\"gloves\"])\n", "\n", "            self.warning_count[idx] = {\n", "                \"person_count\": self.person_count,\n", "                \"no_helmet_count\": self.no_helmet_count,\n", "                \"no_vest_count\": self.no_vest_count,\n", "                \"no_gloves_count\": self.no_gloves_count\n", "            }\n", "\n", "            # print(f\"Total Persons: {self.person_count}, No Helmet: {self.no_helmet_count}, No Vest: {self.no_vest_count}, No Gloves: {self.no_gloves_count}\")\n", "\n", "            # print(compliance_results)\n", "\n", "            # # Draw compliance status on frame\n", "            # for result in compliance_results:\n", "            #     # print(result['bbox'])\n", "            #     x1, y1, x2, y2 = map(int, result[\"bbox\"])\n", "            #     label = []\n", "            #     if result[\"helmet\"]:\n", "            #         label.append(\"Helmet: weared\")\n", "            #     else:\n", "            #         label.append(\"Helmet: not weared\")\n", "\n", "            #     if result[\"vest\"]:\n", "            #         label.append(\"Vest: weared\")\n", "            #     else:\n", "            #         label.append(\"Vest: not weared\")\n", "\n", "            #     if result[\"gloves\"]:\n", "            #         label.append(\"Gloves: weared\")\n", "            #     else:\n", "            #         label.append(\"Gloves: not weared\")\n", "\n", "            #     text = \" | \".join(label)\n", "            #     color = (0, 255, 0) if all([result[\"helmet\"], result[\"vest\"], result[\"gloves\"]]) else (0, 0, 255)\n", "                \n", "            #     cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)\n", "            #     cv2.putText(frame, text, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)\n", "\n", "            # cv2.imshow(\"frame\", frame)\n", "            # cv2.<PERSON><PERSON><PERSON>(1)\n", "            # self.frame = cv2.resize(self.frame, (640, 640),interpolation=cv2.INTER_AREA)\n", "\n", "            _, buffer = cv2.imencode(\".png\", self.frame)\n", "            self.helmet_frames[idx] = buffer.tobytes()\n", "\n", "            if not self.running:\n", "                break\n", "\n", "        # cv2.destroyAllWindows()\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING  Unable to automatically guess model task, assuming 'task=detect'. Explicitly define task for your model, i.e. 'task=detect', 'segment', 'classify','pose' or 'obb'.\n", "Loading C:\\ECOGO PROJECTS\\Bitbucket\\vigilanteye\\sub-development\\vigilanteye\\vigilanteye\\app\\helmet_detection\\models\\seg-model.engine for TensorRT inference...\n", "\n", "0: 640x640 1 helmet, 5 persons, 5 vests, 36.8ms\n", "Speed: 3.9ms preprocess, 36.8ms inference, 4.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 20.3ms\n", "Speed: 8.0ms preprocess, 20.3ms inference, 6.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.8ms\n", "Speed: 7.7ms preprocess, 16.8ms inference, 5.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 18.4ms\n", "Speed: 8.0ms preprocess, 18.4ms inference, 6.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.3ms\n", "Speed: 4.5ms preprocess, 14.3ms inference, 5.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 15.0ms\n", "Speed: 7.0ms preprocess, 15.0ms inference, 5.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 16.7ms\n", "Speed: 5.1ms preprocess, 16.7ms inference, 4.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 16.9ms\n", "Speed: 5.2ms preprocess, 16.9ms inference, 4.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 20.3ms\n", "Speed: 4.0ms preprocess, 20.3ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.2ms\n", "Speed: 6.7ms preprocess, 14.2ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.5ms\n", "Speed: 4.9ms preprocess, 16.5ms inference, 5.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.8ms\n", "Speed: 4.6ms preprocess, 13.8ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.3ms\n", "Speed: 5.5ms preprocess, 14.3ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 5 vests, 16.6ms\n", "Speed: 6.6ms preprocess, 16.6ms inference, 4.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.4ms\n", "Speed: 5.5ms preprocess, 16.4ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.3ms\n", "Speed: 4.2ms preprocess, 14.3ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.4ms\n", "Speed: 5.3ms preprocess, 14.4ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 15.8ms\n", "Speed: 7.8ms preprocess, 15.8ms inference, 4.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 14.9ms\n", "Speed: 5.9ms preprocess, 14.9ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 4 vests, 14.3ms\n", "Speed: 5.4ms preprocess, 14.3ms inference, 5.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 15.3ms\n", "Speed: 6.0ms preprocess, 15.3ms inference, 3.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 4 vests, 13.9ms\n", "Speed: 4.1ms preprocess, 13.9ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 16.7ms\n", "Speed: 4.1ms preprocess, 16.7ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 14.3ms\n", "Speed: 6.1ms preprocess, 14.3ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.9ms\n", "Speed: 6.8ms preprocess, 13.9ms inference, 4.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.1ms\n", "Speed: 6.2ms preprocess, 15.1ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.8ms\n", "Speed: 7.7ms preprocess, 15.8ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.2ms\n", "Speed: 4.8ms preprocess, 15.2ms inference, 3.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.6ms\n", "Speed: 4.5ms preprocess, 14.6ms inference, 4.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 4 vests, 15.1ms\n", "Speed: 4.7ms preprocess, 15.1ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 15.9ms\n", "Speed: 4.0ms preprocess, 15.9ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.2ms\n", "Speed: 4.4ms preprocess, 14.2ms inference, 5.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.1ms\n", "Speed: 6.0ms preprocess, 15.1ms inference, 5.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.0ms\n", "Speed: 5.5ms preprocess, 16.0ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.0ms\n", "Speed: 3.8ms preprocess, 16.0ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 16.2ms\n", "Speed: 4.3ms preprocess, 16.2ms inference, 5.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.5ms\n", "Speed: 4.6ms preprocess, 15.5ms inference, 4.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 3 vests, 15.5ms\n", "Speed: 6.5ms preprocess, 15.5ms inference, 3.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 17.3ms\n", "Speed: 8.4ms preprocess, 17.3ms inference, 5.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 14.1ms\n", "Speed: 4.1ms preprocess, 14.1ms inference, 5.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 14.3ms\n", "Speed: 6.4ms preprocess, 14.3ms inference, 4.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.0ms\n", "Speed: 4.9ms preprocess, 15.0ms inference, 4.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 5 vests, 14.1ms\n", "Speed: 5.1ms preprocess, 14.1ms inference, 3.7ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.4ms\n", "Speed: 7.4ms preprocess, 15.4ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.7ms\n", "Speed: 3.7ms preprocess, 15.7ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.9ms\n", "Speed: 5.6ms preprocess, 15.9ms inference, 3.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.5ms\n", "Speed: 4.1ms preprocess, 14.5ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.2ms\n", "Speed: 4.1ms preprocess, 14.2ms inference, 3.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.2ms\n", "Speed: 4.4ms preprocess, 14.2ms inference, 3.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.7ms\n", "Speed: 5.2ms preprocess, 14.7ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 16.3ms\n", "Speed: 4.1ms preprocess, 16.3ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.9ms\n", "Speed: 4.1ms preprocess, 13.9ms inference, 5.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.8ms\n", "Speed: 4.8ms preprocess, 13.8ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.1ms\n", "Speed: 7.1ms preprocess, 14.1ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 21.9ms\n", "Speed: 5.8ms preprocess, 21.9ms inference, 5.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.6ms\n", "Speed: 6.5ms preprocess, 16.6ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.3ms\n", "Speed: 5.2ms preprocess, 15.3ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.8ms\n", "Speed: 6.6ms preprocess, 14.8ms inference, 3.7ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.2ms\n", "Speed: 6.1ms preprocess, 14.2ms inference, 4.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 15.0ms\n", "Speed: 3.9ms preprocess, 15.0ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 13.9ms\n", "Speed: 5.9ms preprocess, 13.9ms inference, 3.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.4ms\n", "Speed: 6.3ms preprocess, 16.4ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.6ms\n", "Speed: 6.6ms preprocess, 16.6ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.3ms\n", "Speed: 4.4ms preprocess, 15.3ms inference, 5.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.1ms\n", "Speed: 6.7ms preprocess, 14.1ms inference, 5.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 2 helmets, 5 persons, 4 vests, 15.0ms\n", "Speed: 5.0ms preprocess, 15.0ms inference, 3.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 15.0ms\n", "Speed: 4.8ms preprocess, 15.0ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 3 vests, 15.0ms\n", "Speed: 5.1ms preprocess, 15.0ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 3 vests, 17.8ms\n", "Speed: 5.1ms preprocess, 17.8ms inference, 4.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 15.4ms\n", "Speed: 7.1ms preprocess, 15.4ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.3ms\n", "Speed: 5.1ms preprocess, 14.3ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 14.0ms\n", "Speed: 5.9ms preprocess, 14.0ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.7ms\n", "Speed: 6.8ms preprocess, 14.7ms inference, 4.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 17.6ms\n", "Speed: 5.3ms preprocess, 17.6ms inference, 5.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.8ms\n", "Speed: 4.9ms preprocess, 13.8ms inference, 5.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 14.7ms\n", "Speed: 4.2ms preprocess, 14.7ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 15.2ms\n", "Speed: 5.9ms preprocess, 15.2ms inference, 5.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.8ms\n", "Speed: 5.6ms preprocess, 13.8ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.0ms\n", "Speed: 5.4ms preprocess, 14.0ms inference, 5.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.0ms\n", "Speed: 5.5ms preprocess, 14.0ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.0ms\n", "Speed: 5.0ms preprocess, 15.0ms inference, 4.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.7ms\n", "Speed: 5.1ms preprocess, 14.7ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.0ms\n", "Speed: 4.5ms preprocess, 14.0ms inference, 5.2ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 15.1ms\n", "Speed: 4.4ms preprocess, 15.1ms inference, 5.3ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.3ms\n", "Speed: 4.9ms preprocess, 14.3ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 3 vests, 15.3ms\n", "Speed: 6.2ms preprocess, 15.3ms inference, 4.5ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.9ms\n", "Speed: 4.2ms preprocess, 13.9ms inference, 5.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 16.3ms\n", "Speed: 4.4ms preprocess, 16.3ms inference, 4.4ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 14.4ms\n", "Speed: 4.6ms preprocess, 14.4ms inference, 5.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 15.3ms\n", "Speed: 7.6ms preprocess, 15.3ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 16.5ms\n", "Speed: 6.4ms preprocess, 16.5ms inference, 5.7ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 4 persons, 3 vests, 14.6ms\n", "Speed: 6.5ms preprocess, 14.6ms inference, 6.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 14.4ms\n", "Speed: 4.3ms preprocess, 14.4ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 3 vests, 17.2ms\n", "Speed: 4.1ms preprocess, 17.2ms inference, 4.6ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 15.3ms\n", "Speed: 4.6ms preprocess, 15.3ms inference, 5.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 16.0ms\n", "Speed: 4.5ms preprocess, 16.0ms inference, 5.0ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 12.4ms\n", "Speed: 8.1ms preprocess, 12.4ms inference, 3.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 6 persons, 4 vests, 14.1ms\n", "Speed: 6.2ms preprocess, 14.1ms inference, 4.1ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 13.8ms\n", "Speed: 5.6ms preprocess, 13.8ms inference, 4.9ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.9ms\n", "Speed: 6.2ms preprocess, 14.9ms inference, 3.8ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 14.9ms\n", "Speed: 5.4ms preprocess, 14.9ms inference, 4.7ms postprocess per image at shape (1, 3, 640, 640)\n", "\n", "0: 640x640 1 helmet, 5 persons, 4 vests, 15.0ms\n", "Speed: 5.4ms preprocess, 15.0ms inference, 5.8ms postprocess per image at shape (1, 3, 640, 640)\n"]}], "source": ["from ultralytics import YOLO\n", "import cv2\n", "import numpy as np\n", "from collections import defaultdict\n", "\n", "model = YOLO(r\"C:\\ECOGO PROJECTS\\Bitbucket\\vigilanteye\\sub-development\\vigilanteye\\vigilanteye\\app\\helmet_detection\\models\\seg-model.engine\")\n", "\n", "cap = cv2.VideoCapture(r\"C:\\ECOGO PROJECTS\\Bitbucket\\vigilanteye\\sub-development\\vigilanteye\\vigilanteye\\app\\3.mp4\")\n", "\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "\n", "    # Perform inference\n", "    results = model.track(frame, stream=False,persist=True,conf = 0.5, tracker = \"botsort.yaml\")\n", "    boxes = results[0].boxes.xywh.cpu()\n", "    track_ids = results[0].boxes.id.int().cpu().tolist()\n", "    frame = results[0].plot()\n", "    track_history = defaultdict(lambda: [])\n", "    for box, track_id in zip(boxes, track_ids):\n", "            x, y, w, h = box\n", "            track = track_history[track_id]\n", "            track.append((float(x), float(y)))\n", "            if len(track) > 1:\n", "                track.pop(0)\n", "            points = np.hstack(track).astype(np.int32).reshape((-1, 1, 2))\n", "            cv2.polylines(frame, [points], isClosed=False, color=(230, 230, 230), thickness=10)\n", "\n", "    cv2.imshow(\"Frame\", frame)\n", "    if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "        break\n", "cap.release()\n", "cv2.destroyAllWindows()"]}], "metadata": {"kernelspec": {"display_name": "tensorrt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}