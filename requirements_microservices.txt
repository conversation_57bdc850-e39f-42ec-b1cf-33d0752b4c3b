# Core FastAPI and web framework dependencies
fastapi[standard]
uvicorn[standard]
python-multipart
jinja2

# Database dependencies
SQLAlchemy==2.0.40
PyMySQL==1.1.1
alembic==1.15.2

# Authentication and security
python-jose==3.4.0
passlib==1.7.4
bcrypt==4.3.0
cryptography==44.0.3

# Configuration and environment
pydantic==2.11.4
pydantic-settings==2.9.1
python-dotenv==1.0.1

# HTTP client for service communication
requests

# Computer Vision and AI (for services that need them)
opencv-python==*********
ultralytics==8.3.133
deepface==0.0.93
tf-keras==2.19.0
facenet-pytorch==2.5.2
deep-sort-realtime==1.3.2
huggingface-hub==0.31.2
dlib
imutils==0.5.4

# Clustering and data processing
hdbscan
qdrant-client>=1.8.0

# Email functionality
email-validator

# Development and testing
pytest
pytest-asyncio
