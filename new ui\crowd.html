<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - Premium PPE Dashboard</title>
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      /* Make the sidebar fixed */
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto; /* In case the content is too long */
    }
    
    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
    }
    
    .logo svg {
      margin-right: 12px;
    }
    
    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
    }
    
    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }
    
    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }
    
    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }
    
    .system-status {
      margin-top: auto;
      padding: 15px 25px;
      font-size: 13px;
      background-color: rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #4caf50;
    }
    
    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      /* Add margin-left to prevent overlap with fixed sidebar */
      margin-left: 280px;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }
    
    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }
    
    .page-title svg {
      margin-right: 12px;
      color: var(--primary);
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }
    
    .btn svg {
      margin-right: 8px;
    }
    
    .btn-primary {
      background-color: var(--primary);
      color: white;
    }
    
    .btn-primary:hover {
      background-color: var(--primary-dark);
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }
    
    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }
    
    .stat-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .stat-card {
      background-color: var(--card-bg);
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .stat-title {
      font-size: 16px;
      color: var(--gray);
      margin-bottom: 15px;
    }
    
    .stat-value {
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    .stat-subtitle {
      font-size: 13px;
      color: var(--gray);
    }
    
    .total-detections .stat-value {
      color: var(--primary);
    }
    
    .helmets-worn .stat-value {
      color: var(--warning);
    }
    
    .vests-worn .stat-value {
      color: var(--danger);
    }
    
    .gloves-worn .stat-value {
      color: #7e57c2;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--dark);
    }
    
    .camera-feeds {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
    }
    
    .camera-card {
      background-color: var(--card-bg);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }
    
    .camera-feed {
      width: 100%;
      height: 250px;
      background-color: #0d1b42;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .camera-feed img {
      max-width: 100%;
      max-height: 100%;
    }
    
    .status-badge {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 5px 12px;
      border-radius: 50px;
      font-size: 12px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.5);
    }
    
    .live-badge {
      color: white;
    }
    
    .live-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #4caf50;
      margin-right: 6px;
    }
    
    .offline-badge {
      color: white;
    }
    
    .offline-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f44336;
      margin-right: 6px;
    }
    
    .camera-info {
      padding: 20px;
    }
    
    .camera-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .camera-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .camera-last-detection {
      font-size: 13px;
      color: var(--gray);
    }
    
    .detection-count {
      background-color: rgba(46, 125, 50, 0.1);
      color: var(--success);
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
    }
    
    .camera-actions {
      display: flex;
      justify-content: space-between;
    }
    
    .action-btn {
      padding: 8px 15px;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      text-decoration: none;
    }
    
    .expand-btn {
      background-color: rgba(30, 136, 229, 0.1);
      color: var(--primary);
    }
    
    .expand-btn:hover {
      background-color: rgba(30, 136, 229, 0.2);
    }
    
    .alert-btn {
      background-color: var(--success);
      color: white;
    }
    
    .alert-btn:hover {
      background-color: #1b5e20;
    }
    
    .alert-btn svg, .expand-btn svg {
      margin-right: 6px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 1200px) {
      .stat-cards {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .camera-feeds {
        grid-template-columns: 1fr;
      }
    }
    
    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        padding: 10px 0;
      }
      
      .stat-cards {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>
    <a href="Home.html" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>
    <a href="PPE.html" class="nav-item ">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
      </svg>
      PPE Detection
    </a>
    <a href="crowd.html" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
      Crowd Detection
    </a>
    <a href="qc.html" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 20h9"></path>
        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
      </svg>
      Quality Control
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
      Face Detection
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
      Live Feeds
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
      </svg>
      Analytics
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="3"></circle>
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
      </svg>
      Settings
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16 17 21 12 16 7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
      Logout
    </a>
    <!-- <div class="system-status">
      <div><span class="status-indicator"></span> System Online</div>
      <div style="margin-top: 5px; opacity: 0.7;">v2.3.5 | IP: 127.0.0.1:8000</div>
    </div> -->
  </div>

  <div class="content">
    <div class="header">
      <div class="page-title">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
        </svg>
        PPE Detection Dashboard
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Pause Monitoring
        </button>
        <button class="btn btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        </button>
      </div>
    </div>

    <div class="stat-cards">
      <div class="stat-card total-detections">
        <div class="stat-title">Total Detections</div>
        <div class="stat-value">87%</div>
        <div class="stat-subtitle">Total people detected</div>
      </div>
      <div class="stat-card helmets-worn">
        <div class="stat-title">Helmets Worn</div>
        <div class="stat-value">8%</div>
        <div class="stat-subtitle">Number of people wearing helmets</div>
      </div>
      <div class="stat-card vests-worn">
        <div class="stat-title">Vests Worn</div>
        <div class="stat-value">5%</div>
        <div class="stat-subtitle">Number of people wearing vests</div>
      </div>
      <div class="stat-card gloves-worn">
        <div class="stat-title">Gloves Worn</div>
        <div class="stat-value">5%</div>
        <div class="stat-subtitle">Number of people wearing gloves</div>
      </div>
    </div>

    <div class="section-title">Live Camera Feeds</div>
    <div class="camera-feeds">
      <div class="camera-card">
        <div class="camera-feed">
          <img src="/api/placeholder/400/320" alt="Camera Feed 1">
          <div class="status-badge live-badge">LIVE</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 1 - Main Entrance</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 12 sec ago</div>
            <div class="detection-count">9 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="expand.html" class="action-btn expand-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
      
      <div class="camera-card">
        <div class="camera-feed">
          <img src="/api/placeholder/400/320" alt="Camera Feed 2">
          <div class="status-badge live-badge">LIVE</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 2 - Construction Zone A</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 5 sec ago</div>
            <div class="detection-count">6 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="expand.html" class="action-btn expand-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
      
      <div class="camera-card">
        <div class="camera-feed">
          <img src="/api/placeholder/400/320" alt="Camera Feed 3">
          <div class="status-badge offline-badge">OFFLINE</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 3 - Loading Bay</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 2h 15m ago</div>
            <div class="detection-count">0 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="expand.html" class="action-btn expand-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
      
      <div class="camera-card">
        <div class="camera-feed">
          <img src="/api/placeholder/400/320" alt="Camera Feed 4">
          <div class="status-badge live-badge">LIVE</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 4 - Construction Zone B</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 1 min ago</div>
            <div class="detection-count">10 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="expand.html" class="action-btn expand-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
