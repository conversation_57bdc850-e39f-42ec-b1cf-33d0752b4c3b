/* Reset styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f9;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    flex-direction: column;
    padding: 20px;
}

h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

/* Styling the form container */
form {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
}

/* Styling form labels */
label {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 5px;
    display: inline-block;
}

/* Styling form input fields */
input[type="text"],
input[type="email"],
input[type="file"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}



/* Styling the submit button */
button[type="submit"] {
    width: 100%;
    padding: 12px;
    font-size: 1.2rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 20px;
}

button[type="submit"]:hover {
    background-color: #45a049;
}



/* Styling the modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    width: 90%;
    max-width: 400px;
}

.modal-content button {
    margin: 10px 0;
    padding: 10px;
    font-size: 1rem;
    cursor: pointer;
}

/* Styling video and captured image preview */
#camera-container {
    margin-top: 20px;
    text-align: center;
}

video {
    width: 100%;
    max-width: 300px;
    border: 1px solid #ccc;
    margin-bottom: 10px;
}

#image-preview {
    margin-top: 10px;
    max-width: 300px;
    border: 1px solid #ccc;
}

/* Styling buttons */
button {
    background-color: #008CBA;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

button:hover {
    background-color: #005f75;
}



canvas {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}



/* Responsive Design */
@media (max-width: 768px) {
    h2 {
        font-size: 1.8rem;
    }

    form {
        padding: 20px;
    }

    input[type="text"],
    input[type="email"],
    input[type="file"] {
        font-size: 1rem;
    }

    button[type="submit"] {
        font-size: 1rem;
    }
}