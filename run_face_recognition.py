import os
import sys
import time
import logging
from huggingface_hub import hf_hub_download
from app.core.database import SessionLocal
from app.face_recognition.tester import FaceDetection

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # Create a database session that will stay open
    db = SessionLocal()
    
    try:
        # Download the model if needed
        model_path = hf_hub_download(repo_id="arnabdhar/YOLOv8-Face-Detection", filename="model.pt")
        
        # Create the face detection instance with the database session
        detection = FaceDetection(model_path=model_path, db=db)
        
        # Start the face detection with a threshold
        detection.start(threshold=0.7)
        
        if detection.running:
            logger.info("Face recognition system started successfully")
            
            # Keep the script running
            try:
                while detection.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Stopping face recognition system...")
                detection.stop()
        else:
            logger.error("Failed to start face recognition system")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
    finally:
        # Close the database session when done
        db.close()

if __name__ == "__main__":
    main()
