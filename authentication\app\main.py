from fastapi import <PERSON><PERSON><PERSON>, Request, Form, Depends, status, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON><PERSON><PERSON>
from datetime import timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware

# Import authentication modules
from .auth import (
    authenticate_user, create_access_token,
    get_user, create_user, get_user_by_email,
    verify_token, get_current_active_user
)
from .database import get_auth_db, create_auth_tables, AuthSessionLocal
from .config import settings
from .models import Token, OTPCreate, OTPVerify, User
from .email_utils import create_otp, verify_otp, send_otp_email
from .admin import setup_default_users

# Initialize FastAPI app
app = FastAPI(title="Authentication Service", version="1.0.0")

# Create auth database tables and default users
create_auth_tables()
setup_default_users()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="authentication/templates")
app.mount("/static", StaticFiles(directory="authentication/static"), name="static")

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,
        samesite="lax"
    )

    return response

# Logout
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# API token endpoint for programmatic access
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_auth_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

# Registration page
@app.get("/register", response_class=HTMLResponse)
async def register_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})

# Registration endpoints (OTP-based)
@app.post("/request-otp", response_class=JSONResponse)
async def request_otp(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: Session = Depends(get_auth_db)
):
    if password != confirm_password:
        return JSONResponse(
            status_code=400,
            content={"error": "Passwords do not match"}
        )

    existing_user = get_user(db, username)
    if existing_user:
        return JSONResponse(
            status_code=400,
            content={"error": "Username already registered"}
        )

    existing_email = get_user_by_email(db, email)
    if existing_email:
        return JSONResponse(
            status_code=400,
            content={"error": "Email already registered"}
        )

    try:
        otp = create_otp(db, email)
        success = send_otp_email(otp)

        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to send verification email"}
            )

        return JSONResponse(
            content={
                "message": "Verification code sent to admin email. Please enter the code to complete registration.",
                "email": email
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"OTP generation failed: {str(e)}"}
        )

@app.post("/verify-otp", response_class=JSONResponse)
async def verify_otp_and_register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    otp_code: str = Form(...),
    db: Session = Depends(get_auth_db)
):
    is_valid = verify_otp(db, email, otp_code)

    if not is_valid:
        return JSONResponse(
            status_code=400,
            content={"error": "Invalid or expired verification code"}
        )

    try:
        create_user(db, username, email, password, full_name)
        return JSONResponse(
            content={
                "message": "Registration successful! You can now log in.",
                "redirect": "/login"
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Registration failed: {str(e)}"}
        )

# Token verification endpoint for other services
@app.post("/verify-token")
async def verify_token_endpoint(request: dict, db: Session = Depends(get_auth_db)):
    """Verify token for other microservices"""
    try:
        token = request.get("token")
        if not token:
            return {"valid": False}

        from .auth import verify_token as verify_auth_token
        payload = verify_auth_token(token)
        if payload:
            username = payload.get("sub")
            user = get_user(db, username)
            if user:
                return {"valid": True, "user": {"username": user.username, "role": user.role}}
        return {"valid": False}
    except Exception as e:
        print(f"Token verification error: {e}")
        return {"valid": False}

# Home page
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

# API endpoints for other services to use
@app.post("/api/verify-token")
async def verify_token_api(request: dict):
    """API endpoint to verify token for other services"""
    token = request.get("token")
    if not token:
        return {"valid": False, "error": "No token provided"}

    try:
        payload = verify_token(token)
        if payload:
            username = payload.get("sub")
            db = AuthSessionLocal()
            try:
                user = get_user(db, username)
                if user and not user.disabled:
                    return {
                        "valid": True,
                        "username": user.username,
                        "role": user.role,
                        "email": user.email,
                        "full_name": user.full_name
                    }
            finally:
                db.close()
        return {"valid": False, "error": "Invalid token"}
    except Exception as e:
        return {"valid": False, "error": str(e)}

@app.post("/api/authenticate")
async def authenticate_api(request: dict):
    """API endpoint to authenticate user for other services"""
    username = request.get("username")
    password = request.get("password")

    if not username or not password:
        return {"success": False, "error": "Username and password required"}

    try:
        db = AuthSessionLocal()
        try:
            user = authenticate_user(db, username, password)
            if user:
                return {
                    "success": True,
                    "username": user.username,
                    "role": user.role,
                    "email": user.email,
                    "full_name": user.full_name
                }
            return {"success": False, "error": "Invalid credentials"}
        finally:
            db.close()
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.get("/api/user-info")
async def get_user_info_api(current_user: User = Depends(get_current_active_user)):
    """API endpoint to get user info for other services"""
    return {
        "username": current_user.username,
        "role": current_user.role,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "disabled": current_user.disabled
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
