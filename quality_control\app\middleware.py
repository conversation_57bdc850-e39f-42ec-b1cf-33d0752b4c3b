from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse
from auth import verify_token, get_user
from database import AuthSessionLocal

async def auth_middleware(request: Request, call_next):
    """Middleware to verify authentication"""
    
    # Skip authentication for login page, static files, and API endpoints
    if (request.url.path.startswith("/login") or 
        request.url.path.startswith("/static") or
        request.url.path.startswith("/images") or
        request.url.path == "/token"):
        response = await call_next(request)
        return response
    
    # Get token from cookie
    token = request.cookies.get("access_token")
    
    if not token:
        # Redirect to login page
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    
    # Remove "Bearer " prefix if present
    if token.startswith("Bearer "):
        token = token[7:]
    
    try:
        # Verify token
        payload = verify_token(token)
        if payload:
            username = payload.get("sub")
            
            # Get user from database
            db = AuthSessionLocal()
            try:
                user = get_user(db, username)
                if user:
                    # Add user info to request state
                    request.state.user = {"username": user.username, "role": user.role}
                    response = await call_next(request)
                    return response
            finally:
                db.close()
    except Exception as e:
        print(f"Auth middleware error: {e}")
    
    # Token invalid or user not found
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
