from ultralytics import YOLO
import cv2
import datetime
from huggingface_hub import hf_hub_download
from face_recognition.utils import generate_deepface_encoding, get_available_devices, get_username_from_id, calculate_cosine_similarity
from face_recognition.models import Attendance, Encoding


class FaceRecognition:
    def __init__(self, encodings_cache, users_cache, camera_name="System Camera"):
        self.encodings_cache = encodings_cache
        self.users_cache = users_cache
        self.camera_name = camera_name

    def recognize_face(self, face_encoding, db):
        highest_similarity = -1
        most_similar_user = None
        most_similar_user_id = None

        for db_encoding in self.encodings_cache:
            similarity = calculate_cosine_similarity(face_encoding, db_encoding.encoding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                most_similar_user_id = db_encoding.user_id
                most_similar_user = get_username_from_id(most_similar_user_id, self.users_cache)

        similarity_threshold = 0.5
        if most_similar_user and highest_similarity >= similarity_threshold:
            # Log attendance
            time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
            if not db.query(Attendance).filter(
                Attendance.user_id == most_similar_user_id,
                Attendance.timestamp >= time_window_start
            ).first():
                # Create attendance record with camera name
                attendance_record = Attendance(
                    user_id=most_similar_user_id,
                    timestamp=datetime.datetime.now(),
                    camera_name=self.camera_name  # Use the camera name from the FaceRecognition instance
                )
                db.add(attendance_record)
                db.commit()

            return most_similar_user, (0, 255, 0)  # Green for recognized
        return "Unknown", (0, 0, 255)  # Red for unrecognized
