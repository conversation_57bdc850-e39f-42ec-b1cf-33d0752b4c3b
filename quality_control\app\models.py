from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey, DateTime, JSON, Boolean, Float, Text
from database import Base, AuthBase
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import UploadFile


# Quality Control Models
class Inspection(Base):
    __tablename__ = 'inspections'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    camera_name = Column(String(100), nullable=True)
    image_path = Column(String(255), nullable=True)
    product_id = Column(String(100), nullable=True)
    inspection_type = Column(String(50))  # 'defect_detection', 'dimension_check', 'color_analysis'
    result = Column(String(20))  # 'pass', 'fail', 'warning'
    confidence = Column(Float)
    defects_found = Column(JSON, nullable=True)  # List of defects detected
    measurements = Column(JSON, nullable=True)  # Dimensional measurements

    def __repr__(self):
        return f"<Inspection(id={self.id}, type={self.inspection_type}, result={self.result})>"


class Defect(Base):
    __tablename__ = 'defects'

    id = Column(Integer, primary_key=True, index=True)
    inspection_id = Column(Integer, ForeignKey('inspections.id'))
    defect_type = Column(String(50))  # 'scratch', 'dent', 'discoloration', 'crack'
    severity = Column(String(20))  # 'minor', 'major', 'critical'
    bbox_x = Column(Integer)
    bbox_y = Column(Integer)
    bbox_width = Column(Integer)
    bbox_height = Column(Integer)
    confidence = Column(Float)

    inspection = relationship("Inspection")

    def __repr__(self):
        return f"<Defect(id={self.id}, type={self.defect_type}, severity={self.severity})>"


class ProductBatch(Base):
    __tablename__ = 'product_batches'

    id = Column(Integer, primary_key=True, index=True)
    batch_number = Column(String(100), unique=True, index=True)
    product_type = Column(String(100))
    total_items = Column(Integer)
    inspected_items = Column(Integer, default=0)
    passed_items = Column(Integer, default=0)
    failed_items = Column(Integer, default=0)
    start_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    end_time = Column(DateTime, nullable=True)
    status = Column(String(20), default='in_progress')  # 'in_progress', 'completed', 'paused'

    def __repr__(self):
        return f"<ProductBatch(batch={self.batch_number}, status={self.status})>"


class QualityStandard(Base):
    __tablename__ = 'quality_standards'

    id = Column(Integer, primary_key=True, index=True)
    product_type = Column(String(100), index=True)
    standard_name = Column(String(100))
    parameters = Column(JSON)  # Quality parameters and thresholds
    tolerance_levels = Column(JSON)  # Acceptable tolerance ranges
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<QualityStandard(id={self.id}, product={self.product_type}, standard={self.standard_name})>"


class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), nullable=False)
    inspection_zone = Column(JSON, nullable=True)  # Coordinates for inspection area
    camera_type = Column(String(50))  # 'overhead', 'side', 'detail'
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Camera(id={self.id}, name={self.name}, type={self.camera_type})>"


# Auth models for authentication
class AuthUser(AuthBase):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="user")
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# OTP model for verification
class OTP(AuthBase):
    __tablename__ = "otps"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), index=True)
    code = Column(String(6))
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    is_used = Column(Boolean, default=False)

    def is_valid(self):
        """Check if OTP is valid (not expired and not used)"""
        now = datetime.utcnow()
        is_not_used = not self.is_used
        is_not_expired = now < self.expires_at
        return is_not_used and is_not_expired


# Pydantic models for API requests/responses
class InspectionCreate(BaseModel):
    camera_name: str
    product_id: Optional[str] = None
    inspection_type: str
    result: str
    confidence: float
    defects_found: Optional[List[dict]] = None
    measurements: Optional[dict] = None
    image_path: Optional[str] = None


class InspectionResponse(BaseModel):
    id: int
    timestamp: datetime
    camera_name: Optional[str]
    product_id: Optional[str]
    inspection_type: str
    result: str
    confidence: float
    defects_found: Optional[List[dict]]
    measurements: Optional[dict]
    image_path: Optional[str]

    class Config:
        orm_mode = True


class DefectCreate(BaseModel):
    inspection_id: int
    defect_type: str
    severity: str
    bbox_x: int
    bbox_y: int
    bbox_width: int
    bbox_height: int
    confidence: float


class DefectResponse(BaseModel):
    id: int
    inspection_id: int
    defect_type: str
    severity: str
    bbox_x: int
    bbox_y: int
    bbox_width: int
    bbox_height: int
    confidence: float

    class Config:
        orm_mode = True


class ProductBatchCreate(BaseModel):
    batch_number: str
    product_type: str
    total_items: int


class ProductBatchResponse(BaseModel):
    id: int
    batch_number: str
    product_type: str
    total_items: int
    inspected_items: int
    passed_items: int
    failed_items: int
    start_time: datetime
    end_time: Optional[datetime]
    status: str

    class Config:
        orm_mode = True


class QualityStandardCreate(BaseModel):
    product_type: str
    standard_name: str
    parameters: dict
    tolerance_levels: dict


class QualityStandardResponse(BaseModel):
    id: int
    product_type: str
    standard_name: str
    parameters: dict
    tolerance_levels: dict
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class CameraCreate(BaseModel):
    name: str
    rtsp_url: str
    inspection_zone: Optional[List[int]] = None
    camera_type: str = 'overhead'


class CameraResponse(BaseModel):
    id: int
    name: str
    rtsp_url: str
    inspection_zone: Optional[List[int]]
    camera_type: str
    is_active: bool
    created_at: datetime

    class Config:
        orm_mode = True


# Token models for authentication
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# OTP Pydantic models
class OTPCreate(BaseModel):
    email: EmailStr

class OTPVerify(BaseModel):
    email: EmailStr
    code: str
