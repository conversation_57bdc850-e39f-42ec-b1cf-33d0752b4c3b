<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Camera Feed</title>
    <link rel="stylesheet" href="{{ url_for('add_roi_static', path='addroi.css') }}">
</head>

<body>
    <div class="main-container">
        <!-- Left Container for Controls -->
        <div class="left-container">
            <div>

                <div class="logo">
                    <img src="/static/logo.png" alt="Logo"><br>
                </div>


                <div class="buttons">
                    <br>
                    <br>
                    <br>
                    <!-- <h2>Camera Controls</h2> -->
                    <button onclick="startCamera()">Start Camera</button>
                    <button onclick="stopCamera()">Stop Camera</button>
                    <button onclick="changeCamera('prev')">Previous Camera</button>
                    <button onclick="changeCamera('next')">Next Camera</button>
                    <button onclick="sendCoordinates()">Save Changes</button>
                    <button onclick="clearPolygons()">Clear All</button>
                    <br>
                    <!-- <p id="camera-info">CAMERA:0 IS SELECTED</p> Display current camera index -->
                </div>
            </div>

            <button class="homepage-btn" id="exitBtn">Exit</button>

        </div>

        <!-- Right Container for Video and Canvas -->
        <div class="right-container">
            <h2>Camera Feed</h2>
            <h3 id="camera-info"></h3> <!-- Display current camera index -->
            <div id="container">
                <!-- Video Feed -->
                <img  id="video" alt="Camera Feed" class="fade-out" width="640" height="480" />
                <!-- Canvas for Drawing -->
                <canvas id="canvas" width="640" class="fade-out" height="480"></canvas>

                <div class="instruction-div">
                    <h3>Please click the 'Start Camera' button in the sidebar to begin.</h3>
                </div>
            </div>
        </div>
    </div>
        <script>

            let videoElement = document.getElementById("video");
            let canvas = document.getElementById("canvas");
            let instructionDiv = document.querySelector(".instruction-div");
            let ctx = canvas.getContext("2d");
            let cameraInfo = document.getElementById("camera-info");
            let cam_name = "";  // Declare globally

            // Get camera ID and name from URL parameters if available
            const urlParams = new URLSearchParams(window.location.search);
            const urlCameraId = urlParams.get('camera_id');
            const urlCameraName = urlParams.get('camera_name');

            // If camera name is provided in URL, use it
            if (urlCameraName) {
                cam_name = urlCameraName;
                document.getElementById("camera-info").innerHTML = `<h3>${cam_name} SELECTED</h3>`;
            }

            let currentCameraIndex = urlCameraId ? parseInt(urlCameraId) : 0;  // Use camera ID from URL if available
            let isDrawing = false;       // Track if the user is drawing
            let polygons = [];           // Store multiple polygons
            let currentPolygon = [];     // Store coordinates of the current polygon

            const exitBtn = document.getElementById("exitBtn");
            let ws = null;



            // Event listener for the "Exit" button
            exitBtn.addEventListener("click", () => {
                // If we came from the expanded view, go back to it
                if (urlCameraId && urlCameraName) {
                    window.location.href = `/crowd_detection/expand?id=${urlCameraId}&name=${encodeURIComponent(urlCameraName)}`;
                } else {
                    // Otherwise go back to the main crowd detection page
                    window.location.href = "/crowd_detection";
                }
            });


            // function startCamera() {
            //     console.log("Starting camera...");
            //     fetch("/crowd_detection/camera/start")
            //         .then((response) => response.json())
            //         .then(() => {
            //             ws = new WebSocket("ws://localhost:8000/crowd_detection/addroi");
            //             ws.onmessage = (event) => {
            //                 const message = event.data;
            //                 try {
            //                     const parsedData = JSON.parse(message); // Parse JSON response
            //                     const frame = parsedData.frame; // Extract the frame
            //                     cam_name = parsedData.camera_name;
            //                     document.getElementById('camera-info').innerHTML = `<h3>${cam_name} SELECTED</h3>`;
            //                     if (frame) {
            //                         videoElement.src = `data:image/jpeg;base64,${frame}`; // Set image src directly
            //                     }
            //                 } catch (error) {
            //                     console.error("Error parsing WebSocket message:", error);
            //                 }
            //             };
            //             ws.onclose = () => {
            //                 console.error("WebSocket connection closed");
            //                 alert("WebSocket connection closed");
            //             };
            //         })
            //         .catch((error) => console.error("Error starting camera:", error));
            // }

            function startStopToggle(type) {

                if (!videoElement || !canvas || !instructionDiv) {
                    console.error("One or more elements not found!");
                    return;
                }

                if (type === "stop") {
                    // Stop Camera: Hide Video & Canvas, Show Instructions
                    videoElement.classList.remove("fade-in");
                    videoElement.classList.add("fade-out");

                    canvas.classList.remove("fade-in");
                    canvas.classList.add("fade-out");

                    instructionDiv.classList.remove("fade-out");
                    instructionDiv.classList.add("fade-in");
                } else {
                    // Start Camera: Show Video & Canvas, Hide Instructions
                    videoElement.classList.remove("fade-out");
                    videoElement.classList.add("fade-in");

                    canvas.classList.remove("fade-out");
                    canvas.classList.add("fade-in");

                    instructionDiv.classList.remove("fade-in");
                    instructionDiv.classList.add("fade-out");
                }
            }


            function startCamera() {
                setTimeout(() => {
                    startStopToggle('start');
                }, 2000);
                console.log("Starting camera...");
                fetch("/crowd_detection/camera/start")
                    .then((response) => response.json())
                    .then(() => {
                        // Use the correct WebSocket URL with proper protocol
                        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                        const wsHost = window.location.host;
                        const wsUrl = `${wsProtocol}//${wsHost}/crowd_detection/addroi`;

                        console.log("Connecting to WebSocket at:", wsUrl);
                        ws = new WebSocket(wsUrl);
                        ws.binaryType = "arraybuffer"; // Expect binary data

                        ws.onmessage = (event) => {
                            const data = new Uint8Array(event.data);
                            const separatorIndex = data.indexOf(10); // Find newline separator

                            if (separatorIndex === -1) {
                                console.error("Invalid data format received.");
                                return;
                            }

                            const metadataJson = new TextDecoder().decode(data.slice(0, separatorIndex));
                            const frameData = data.slice(separatorIndex + 1);

                            try {
                                const metadata = JSON.parse(metadataJson);

                                // Only update cam_name if it wasn't set from URL parameters
                                if (!urlCameraName) {
                                    cam_name = metadata.camera_name;
                                    document.getElementById("camera-info").innerHTML = `<h3>${cam_name} SELECTED</h3>`;
                                }

                                const blob = new Blob([frameData], { type: "image/jpeg" });
                                const url = URL.createObjectURL(blob);
                                videoElement.src = url; // Update video feed

                                // Clean up the URL object to avoid memory leaks
                                setTimeout(() => {
                                    URL.revokeObjectURL(url);
                                }, 1000);
                            } catch (error) {
                                console.error("Error parsing WebSocket message:", error);
                            }
                        };

                        ws.onopen = () => {
                            console.log("WebSocket connection established");

                            // If we have a camera ID from URL, navigate to that camera
                            if (urlCameraId) {
                                // Navigate to the correct camera based on the URL parameter
                                const targetIndex = parseInt(urlCameraId);
                                let currentIdx = 0;

                                // Function to navigate to the target camera
                                const navigateToCamera = () => {
                                    if (currentIdx < targetIndex) {
                                        changeCamera('next');
                                        currentIdx++;
                                        setTimeout(navigateToCamera, 500); // Wait a bit between camera changes
                                    }
                                };

                                // Start navigation after a short delay
                                setTimeout(navigateToCamera, 1000);
                            }
                        };

                        ws.onerror = (error) => {
                            console.error("WebSocket error:", error);
                        };

                        ws.onclose = () => {
                            console.error("WebSocket connection closed");
                        };
                    })
                    .catch((error) => console.error("Error starting camera:", error));
            }

            function stopCamera() {
                startStopToggle('stop');
                fetch("/crowd_detection/camera/stop")
                    .then((response) => response.json())
                    .then(() => {
                        if (ws) {
                            ws.close();
                            ws = null;
                        }
                        videoElement.src = ""; // Clear video feed
                    })
                    .catch((error) => console.error("Error stopping camera:", error));
            }
            function changeCamera(action) {
                // Clear canvas when switching to a new camera
                ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear any existing polygons
                polygons = []; // Reset the polygons array
                currentPolygon = []; // Reset the current polygon

                fetch(`/crowd_detection/camera/${action}`, {
                    method: "POST",
                })
                    .then((response) => response.json())
                    .then((data) => {
                        console.log(data);
                        if (action === "next") {
                            currentCameraIndex = (currentCameraIndex + 1) % data.total_cameras;
                        } else {
                            currentCameraIndex = (currentCameraIndex - 1 + data.total_cameras) % data.total_cameras;
                        }
                        updateCameraInfo(); // Update the camera info text when camera is changed
                    })
                    .catch((error) => console.error("Error:", error));
            }

            function updateCameraInfo() {
                // Update the displayed camera info with the format CAM:0, CAM:1
                cameraInfo.innerText = `${cam_name} SELECTED`;
            }

            // Draw polygon on canvas
            canvas.addEventListener("mousedown", (event) => {
                isDrawing = true;
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                currentPolygon.push([x, y]); // Add coordinate to current polygon
            });

            canvas.addEventListener("mouseup", () => {
                isDrawing = false;
            });

            canvas.addEventListener("mousemove", (event) => {
                if (!isDrawing) return;

                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                currentPolygon.push([x, y]);
                drawPolygons();
            });

            // Double-click to close the current polygon
            canvas.addEventListener("dblclick", () => {
                if (currentPolygon.length > 2) {
                    currentPolygon.push(currentPolygon[0]); // Close the polygon
                    polygons.push(currentPolygon); // Add to the list of polygons
                    currentPolygon = []; // Reset for the next polygon
                    drawPolygons();
                }
            });

            // Helper: Draw the polygons on the canvas
            function drawPolygons() {
                ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas

                // Draw all the polygons
                for (let i = 0; i < polygons.length; i++) {
                    const polygon = polygons[i];
                    ctx.beginPath();
                    for (let j = 0; j < polygon.length; j++) {
                        const [x, y] = polygon[j];
                        if (j === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                    ctx.closePath();
                    ctx.strokeStyle = "red";
                    ctx.lineWidth = 2;
                    ctx.stroke();

                    // Calculate the centroid of the polygon to place the label
                    const centroid = getCentroid(polygon);
                    ctx.fillStyle = "red";
                    ctx.font = "16px Arial";
                    ctx.fillText(`ROI:${i}`, centroid[0] + 5, centroid[1] - 5);  // Place label near the centroid
                }

                // Draw the current polygon if still being drawn
                if (currentPolygon.length > 0) {
                    ctx.beginPath();
                    for (let i = 0; i < currentPolygon.length; i++) {
                        const [x, y] = currentPolygon[i];
                        if (i === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                    ctx.strokeStyle = "blue";
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }
            }

            // Function to calculate the centroid of a polygon
            function getCentroid(polygon) {
                let xSum = 0;
                let ySum = 0;
                for (let i = 0; i < polygon.length; i++) {
                    xSum += polygon[i][0];
                    ySum += polygon[i][1];
                }
                return [xSum / polygon.length, ySum / polygon.length];
            }

        function sendCoordinates() {
            const cameraName = `${cam_name}`; // Example camera name
            fetch("/crowd_detection/send-coordinates", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    cameraName: cameraName,
                    coordinates: polygons,
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    console.log("Response:", data);
                })
                .catch((error) => console.error("Error sending coordinates:", error));
        }
        // Clear all polygons and reset the canvas
        function clearPolygons() {
            // Clear the stored polygons
            polygons = [];
            currentPolygon = [];

            // Clear the canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            console.log("Polygons cleared");
        }

        </script>

</body>
</html>