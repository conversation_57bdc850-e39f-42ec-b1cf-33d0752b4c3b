<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Quality Control System</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .header h1 {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.2rem;
      opacity: 0.8;
    }

    .service-info {
      background: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 15px;
      margin: 30px 0;
      text-align: center;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 30px 0;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-primary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-success {
      background: #2ecc71;
      color: white;
    }

    .btn-success:hover {
      background: #27ae60;
      transform: translateY(-2px);
    }

    .btn-danger {
      background: #e74c3c;
      color: white;
    }

    .btn-danger:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #e74c3c;
      transition: background-color 0.3s ease;
    }

    .status-dot.active {
      background: #2ecc71;
    }

    .video-section {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 15px;
      margin: 20px 0;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 10px;
      text-align: center;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .logout-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .home-btn {
      position: absolute;
      top: 20px;
      left: 20px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s ease;
    }

    .home-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  </style>
</head>
<body>
  <a href="../index.html" class="home-btn">🏠 Home</a>

  <div class="container">
    <div class="header">
      <h1>Quality Control System</h1>
      <p>Quality Inspection Service - Port 8004</p>
    </div>

    <div class="service-info">
      <h2>Quality Control & Inspection</h2>
      <p>This is an independent FastAPI application for quality control and product inspection.</p>
      <p>Features include defect detection, dimensional analysis, and quality standards compliance.</p>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalInspections">0</div>
        <div class="stat-label">Total Inspections</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="passedInspections">0</div>
        <div class="stat-label">Passed</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="failedInspections">0</div>
        <div class="stat-label">Failed</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="passRate">0%</div>
        <div class="stat-label">Pass Rate</div>
      </div>
    </div>

    <div class="status-indicator">
      <div class="status-dot" id="statusDot"></div>
      <span id="statusText">System Inactive</span>
    </div>

    <div class="controls">
      <button class="btn btn-success" id="startBtn">Start Inspection</button>
      <button class="btn btn-danger" id="stopBtn">Stop Inspection</button>
      <a href="/cameras" class="btn btn-primary">Manage Cameras</a>
      <a href="/inspections" class="btn btn-primary">View Inspections</a>
      <a href="/batches" class="btn btn-primary">Product Batches</a>
      <a href="/quality-standards" class="btn btn-primary">Quality Standards</a>
      {% if user_role == "admin" %}
      <a href="/admin" class="btn btn-primary">Admin Panel</a>
      {% endif %}
    </div>

    <div class="video-section" id="videoSection">
      <h3>Inspection Cameras</h3>
      <div id="feeds">
        <p>Click "Start Inspection" to begin quality control</p>
      </div>
    </div>
  </div>

  <script>
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const feedsDiv = document.getElementById('feeds');

    let isInspecting = false;

    function updateStatus(active) {
      if (active) {
        statusDot.classList.add('active');
        statusText.textContent = 'System Active - Quality Inspection';
      } else {
        statusDot.classList.remove('active');
        statusText.textContent = 'System Inactive';
      }
    }

    function loadStats() {
      fetch('/stats')
        .then(response => response.json())
        .then(data => {
          document.getElementById('totalInspections').textContent = data.total_inspections || 0;
          document.getElementById('passedInspections').textContent = data.passed_inspections || 0;
          document.getElementById('failedInspections').textContent = data.failed_inspections || 0;
          document.getElementById('passRate').textContent = (data.pass_rate || 0) + '%';
        })
        .catch(error => console.error('Error loading stats:', error));
    }

    startBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/start-quality_control', { method: 'POST' });
        const result = await response.json();

        if (response.ok) {
          isInspecting = true;
          updateStatus(true);
          feedsDiv.innerHTML = '<p>Quality control inspection started successfully</p>';
          alert('Quality control started successfully!');
          loadStats();
        } else {
          alert('Failed to start: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });

    stopBtn.addEventListener('click', async () => {
      try {
        const response = await fetch('/stop-inspection', { method: 'POST' });
        const result = await response.json();

        if (response.ok) {
          isInspecting = false;
          updateStatus(false);
          feedsDiv.innerHTML = '<p>Click "Start Inspection" to begin quality control</p>';
          alert('Quality control stopped successfully!');
        } else {
          alert('Failed to stop: ' + result.message);
        }
      } catch (error) {
        alert('Error: ' + error.message);
      }
    });

    // Load initial stats
    loadStats();

    // Refresh stats every 30 seconds
    setInterval(loadStats, 30000);
  </script>
</body>
</html>
