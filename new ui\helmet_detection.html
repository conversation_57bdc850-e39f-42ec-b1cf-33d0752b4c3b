<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - Premium PPE Dashboard</title>
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      /* Make the sidebar fixed */
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto; /* In case the content is too long */
    }
    
    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
    }
    
    .logo svg {
      margin-right: 12px;
    }
    
    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
    }
    
    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }
    
    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }
    
    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }
    
    .system-status {
      margin-top: auto;
      padding: 15px 25px;
      font-size: 13px;
      background-color: rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #4caf50;
    }
    
    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      /* Add margin-left to prevent overlap with fixed sidebar */
      margin-left: 280px;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }
    
    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }
    
    .page-title svg {
      margin-right: 12px;
      color: var(--primary);
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }
    
    .btn svg {
      margin-right: 8px;
    }
    
    .btn-primary {
      background-color: var(--primary);
      color: white;
    }
    
    .btn-primary:hover {
      background-color: var(--primary-dark);
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }
    
    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }
    
    .stat-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .stat-card {
      background-color: var(--card-bg);
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .stat-title {
      font-size: 16px;
      color: var(--gray);
      margin-bottom: 15px;
    }
    
    .stat-value {
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    .stat-subtitle {
      font-size: 13px;
      color: var(--gray);
    }
    
    .total-detections .stat-value {
      color: var(--primary);
    }
    
    .helmets-worn .stat-value {
      color: var(--warning);
    }
    
    .vests-worn .stat-value {
      color: var(--danger);
    }
    
    .gloves-worn .stat-value {
      color: var(--success);
    }
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--dark);
    }
    
    .camera-feeds {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
    }
    
    .camera-card {
      background-color: var(--card-bg);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }
    
    .camera-feed {
      width: 100%;
      height: 250px;
      background-color: #0d1b42;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .camera-feed img,
    .camera-feed video {
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .status-badge {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 5px 12px;
      border-radius: 50px;
      font-size: 12px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.5);
    }
    
    .live-badge {
      color: white;
    }
    
    .live-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #4caf50;
      margin-right: 6px;
    }
    
    .offline-badge {
      color: white;
    }
    
    .offline-badge::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f44336;
      margin-right: 6px;
    }
    
    .camera-info {
      padding: 20px;
    }
    
    .camera-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .camera-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .camera-last-detection {
      font-size: 13px;
      color: var(--gray);
    }
    
    .detection-count {
      background-color: rgba(46, 125, 50, 0.1);
      color: var(--success);
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
    }
    
    .camera-actions {
      display: flex;
      justify-content: space-between;
    }
    
    .action-btn {
      padding: 8px 15px;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      text-decoration: none;
    }
    
    .expand-btn {
      background-color: rgba(30, 136, 229, 0.1);
      color: var(--primary);
    }
    
    .expand-btn:hover {
      background-color: rgba(30, 136, 229, 0.2);
    }
    
    .alert-btn {
      background-color: var(--success);
      color: white;
    }
    
    .alert-btn:hover {
      background-color: #1b5e20;
    }
    
    .alert-btn svg, .expand-btn svg {
      margin-right: 6px;
    }
    
    /* Status Indicator for Streaming */
    .stream-status {
      position: absolute;
      bottom: 15px;
      left: 15px;
      padding: 5px 12px;
      border-radius: 50px;
      font-size: 12px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
    }
    
    .streaming {
      background-color: rgba(46, 125, 50, 0.7);
    }
    
    .not-streaming {
      background-color: rgba(211, 47, 47, 0.7);
    }
    
    /* Responsive adjustments */
    @media (max-width: 1200px) {
      .stat-cards {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .camera-feeds {
        grid-template-columns: 1fr;
      }
    }
    
    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        padding: 10px 0;
      }
      
      .stat-cards {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>
    <a href="Home.html" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>
    <a href="PPE.html" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
      </svg>
      PPE Detection
    </a>
    <a href="crowd.html" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
      Crowd Detection
    </a>
    <a href="qc.html" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 20h9"></path>
        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
      </svg>
      Quality Control
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
      Face Detection
    </a>
    <!-- <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
      Live Feeds
    </a> -->
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
      </svg>
      Analytics
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="3"></circle>
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
      </svg>
      Settings
    </a>
    <a href="#" class="nav-item" id="uploadVideo">
      <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" preserveAspectRatio="xMidYMid meet">
        <path d="M23 11v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-10m10 0v9m-3-3l3 3 3-3"/>
      </svg>
      Upload Video
    </a>
    <a href="#" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16 17 21 12 16 7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
      Logout
    </a>
  </div>

  <div class="content">
    <div class="header">
      <div class="page-title">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
        </svg>
        PPE Detection Dashboard
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline" id="stopBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Stop Monitoring
        </button>
        <button class="btn btn-primary" id="startBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        </button>
      </div>
    </div>

    <div class="stat-cards">
      <div class="stat-card total-detections">
        <div class="stat-title">Total Detections</div>
        <div class="stat-value">87%</div>
        <div class="stat-subtitle">Total people detected</div>
      </div>
      <div class="stat-card helmets-worn">
        <div class="stat-title">Helmets Worn</div>
        <div class="stat-value">8%</div>
        <div class="stat-subtitle">Number of people wearing helmets</div>
      </div>
      <div class="stat-card vests-worn">
        <div class="stat-title">Vests Worn</div>
        <div class="stat-value">5%</div>
        <div class="stat-subtitle">Number of people wearing vests</div>
      </div>
      <div class="stat-card gloves-worn">
        <div class="stat-title">Gloves Worn</div>
        <div class="stat-value">5%</div>
        <div class="stat-subtitle">Number of people wearing gloves</div>
      </div>
    </div>

    <div class="section-title">Live Camera Feeds</div>
    <div class="camera-feeds">
      <div class="camera-card">
        <div class="camera-feed" id="camera-1">
          <img id="video-1" />
          <div class="status-badge live-badge">LIVE</div>
          <div class="stream-status not-streaming" id="stream-status-1">NOT STREAMING</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 1 - Main Entrance</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 12 sec ago</div>
            <div class="detection-count">9 helmets detected</div>
          </div>
          <div class="camera-actions">
           
            <a href="/helmet_detection/expand?camera=Camera%201&name=Main%20Entrance" class="action-btn expand-btn expand-link">
  
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
      
      <div class="camera-card">
        <div class="camera-feed" id="camera-2">
          <img id="video-2" />
          <div class="status-badge live-badge">LIVE</div>
          <div class="stream-status not-streaming" id="stream-status-2">NOT STREAMING</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 2 - Construction Zone A</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 5 sec ago</div>
            <div class="detection-count">6 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="/helmet_detection/expand?camera=Camera%202&name=Construction%20Zone%20A" class="action-btn expand-btn expand-link">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
      
      <div class="camera-card">
        <div class="camera-feed" id="camera-3">
          <img id="video-3" />
          <div class="status-badge offline-badge">OFFLINE</div>
          <div class="stream-status not-streaming" id="stream-status-3">NOT STREAMING</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 3 - Loading Bay</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 2h 15m ago</div>
            <div class="detection-count">0 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="/helmet_detection/expand?camera=Camera%203&name=Loading%20Bay" class="action-btn expand-btn expand-link">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>

      <div class="camera-card">
        <div class="camera-feed" id="camera-4">
          <img id="video-4" />
          <div class="status-badge live-badge">LIVE</div>
          <div class="stream-status not-streaming" id="stream-status-4">NOT STREAMING</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">Camera 4 - Construction Zone B</div>
          <div class="camera-meta">
            <div class="camera-last-detection">Last detection: 10 sec ago</div>
            <div class="detection-count">7 helmets detected</div>
          </div>
          <div class="camera-actions">
            <a href="/helmet_detection/expand?camera=Camera%204&name=Construction%20Zone%20B" class="action-btn expand-btn expand-link">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </a>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>

      // Main variables
      const startBtn = document.getElementById('startBtn');
      const stopBtn = document.getElementById('stopBtn');
      let websocket = null;
      let isStreaming = false;

      // Initialize camera feeds and observation data
      const cameraData = {
        1: { name: "Camera 1 - Main Entrance", status: "live" },
        2: { name: "Camera 2 - Construction Zone A", status: "live" },
        3: { name: "Camera 3 - Loading Bay", status: "offline" },
        4: { name: "Camera 4 - Construction Zone B", status: "live" }
      };

      // Get statistics elements for updating
      const totalDetectionsValue = document.querySelector('.total-detections .stat-value');
      const helmetsWornValue = document.querySelector('.helmets-worn .stat-value');
      const vestsWornValue = document.querySelector('.vests-worn .stat-value');
      const glovesWornValue = document.querySelector('.gloves-worn .stat-value');

      // Attach event listeners
      startBtn.addEventListener('click', startStream);
      stopBtn.addEventListener('click', stopStream);

      // Add camera management functionality
      document.addEventListener('DOMContentLoaded', function() {
        // Create modal element for camera management
        const modalHTML = `
          <div id="manageCameraModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div class="modal-content" style="background-color: #fefefe; margin: 5% auto; padding: 20px; border-radius: 10px; width: 50%; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
              <span class="close-btn" id="closeModalBtn" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
              <h2><i class="fas fa-video" style="margin-right: 10px;"></i>Add RTSP Camera</h2>
              <form id="cameraForm">
                <div style="margin-bottom: 15px;">
                  <label for="cameraName" style="display: block; margin-bottom: 5px; font-weight: 500;">Camera Name (Unique):</label>
                  <input type="text" id="cameraName" name="cameraName" placeholder="Enter camera name" required style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
                </div>
                
                <div style="margin-bottom: 20px;">
                  <label for="rtspUrl" style="display: block; margin-bottom: 5px; font-weight: 500;">RTSP URL:</label>
                  <input type="text" id="rtspUrl" name="rtspUrl" placeholder="rtsp://" required style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
                </div>
                
                <div style="display: flex; gap: 10px;">
                  <button type="submit" style="background-color: var(--primary); color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer;">Add Camera</button>
                  <button type="button" onclick="toggleCameraList()" style="background-color: transparent; border: 1px solid var(--primary); color: var(--primary); padding: 10px 15px; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-list" style="margin-right: 5px;"></i> Show Cameras
                  </button>
                </div>
                
                <div style="display: none; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;" id="cameraList">
                  <!-- Cameras will be listed here -->
                </div>
              </form>
            </div>
          </div>
        `;
        
        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Add camera management button to sidebar
        const logoutLink = document.querySelector('.sidebar a:last-child');
        const cameraMgmtLink = document.createElement('a');
        cameraMgmtLink.href = "#";
        cameraMgmtLink.className = "nav-item";
        cameraMgmtLink.id = "manageCameraBtn";
        cameraMgmtLink.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 4h6v6"></path>
            <path d="M4 14h6v6"></path>
            <path d="M20 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7"></path>
          </svg>
          Camera Management
        `;
        
        if (logoutLink) {
          logoutLink.parentNode.insertBefore(cameraMgmtLink, logoutLink);
        }
        
        // Setup modal functionality
        const manageCameraBtn = document.getElementById('manageCameraBtn');
        const modal = document.getElementById('manageCameraModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cameraForm = document.getElementById('cameraForm');

        // Show modal when camera management button is clicked
        if (manageCameraBtn) {
          manageCameraBtn.addEventListener('click', () => {
            modal.style.display = "block";
            loadCameras();
          });
        }

        // Close modal when X is clicked
        if (closeModalBtn) {
          closeModalBtn.addEventListener('click', () => {
            modal.style.display = "none";
          });
        }

        // Close modal when clicking outside
        window.addEventListener('click', (event) => {
          if (event.target === modal) {
            modal.style.display = "none";
          }
        });

        // Handle form submission
        if (cameraForm) {
          cameraForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const cameraName = document.getElementById('cameraName').value;
            const rtspUrl = document.getElementById('rtspUrl').value;
            
            try {
              const response = await fetch('/helmet_detection/add-camera', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cameraName, rtspUrl }),
              });

              const result = await response.json();
              
              if (result.status === 'success') {
                showNotification('Camera added successfully!', 'success');
                cameraForm.reset();
                loadCameras();
                loadCameraCards();
              } else if (result.status === 'error') {
                showNotification(`RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
              } else if (result.status === 'samename') {
                showNotification('Camera name already exists', 'error');
              } else {
                showNotification('Error adding camera.', 'error');
              }
            } catch (error) {
              showNotification('Network error. Please try again.', 'error');
              console.error('Error:', error);
            }
          });
        }
      });

      async function loadCameraCards() {
        const container = document.querySelector('.camera-feeds');
        container.innerHTML = ""; // Clear existing cards

        try {
          const response = await fetch("/helmet_detection/get-cameras");
          const cameras = await response.json();

          let cameraId = 1;
          for (const [cameraName, rtspData] of Object.entries(cameras)) {
            const card = createCameraCard(cameraId, cameraName);
            container.appendChild(card);
            cameraId++;
          }
        } catch (error) {
          console.error("Failed to load camera cards:", error);
        }
      }

      // Ensure camera cards are rendered on load
      document.addEventListener("DOMContentLoaded", async function () {
        await loadCameraCards();
        if (sessionStorage.getItem("helmetStreaming") === "true") {
          startStream();
        }
      });

      // Function to start the video stream
      async function startStream() {
        if (isStreaming) return;
        
        try {
          startBtn.disabled = true;
          startBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="fa-spin">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M16 12h2"></path>
              <path d="M8 12h2"></path>
              <path d="M12 16v2"></path>
              <path d="M12 8v2"></path>
            </svg>
            Starting...
          `;
          
          const response = await fetch('/helmet_detection/start-helmet_detection', {
            method: 'POST'
          });
          
          if (response.ok) {
            console.log("Stream started successfully");
            startWebSocketFeed();
            
            for (let i = 1; i <= 4; i++) {
              const statusElement = document.getElementById(`stream-status-${i}`);
              if (statusElement && cameraData[i].status === 'live') {
                statusElement.textContent = 'STREAMING';
                statusElement.classList.remove('not-streaming');
                statusElement.classList.add('streaming');
              }
            }
            
            isStreaming = true;
            sessionStorage.setItem("helmetStreaming", "true");
            startBtn.disabled = false;
            startBtn.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
              </svg>
              Monitoring Active
            `;
          } else {
            console.error("Failed to start stream");
            showNotification('Failed to start streaming', 'error');
            startBtn.disabled = false;
            startBtn.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              Start Monitoring
            `;
          }
        } catch (error) {
          console.error("Error starting stream:", error);
          showNotification('Network error while starting stream', 'error');
          startBtn.disabled = false;
          startBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            Start Monitoring
          `;
        }
      }

      // Function to stop the video stream
      async function stopStream() {
        if (!isStreaming) return;
        
        try {
          stopBtn.disabled = true;
          stopBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="fa-spin">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M16 12h2"></path>
              <path d="M8 12h2"></path>
              <path d="M12 16v2"></path>
              <path d="M12 8v2"></path>
            </svg>
            Stopping...
          `;
          
          if (websocket) {
            websocket.close(); // Close the WebSocket connection
          }
          
          const response = await fetch('/helmet_detection/stop-stream', { 
            method: 'POST' 
          });
          
          if (response.ok) {
            console.log("Stream stopped successfully");
            
            // Clear video feeds and reset status indicators
            for (let i = 1; i <= 4; i++) {
              const imgElement = document.getElementById(`video-${i}`);
              const statusElement = document.getElementById(`stream-status-${i}`);
              
              if (imgElement) {
                imgElement.src = '';
              }
              
              if (statusElement) {
                statusElement.textContent = 'NOT STREAMING';
                statusElement.classList.remove('streaming');
                statusElement.classList.add('not-streaming');
              }
            }
            
            isStreaming = false;
            sessionStorage.removeItem("helmetStreaming");
            startBtn.disabled = false;
            startBtn.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              Start Monitoring
            `;
          } else {
            console.error("Failed to stop stream");
            showNotification('Failed to stop streaming', 'error');
          }
          
          stopBtn.disabled = false;
          stopBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
            </svg>
            Stop Monitoring
          `;
        } catch (error) {
          console.error("Error stopping stream:", error);
          showNotification('Network error while stopping stream', 'error');
          stopBtn.disabled = false;
          stopBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
            </svg>
            Stop Monitoring
          `;
        }
      }

      // Function to start the WebSocket connection and handle incoming frames and counts
      function startWebSocketFeed() {
        websocket = new WebSocket("ws://localhost:8000/helmet_detection/ws");
        
        websocket.binaryType = 'arraybuffer';
        
        let lastMetadata = null;
        let totalStats = {
          total: 0,
          noHelmet: 0,
          noVest: 0,
          noGloves: 0
        };

        websocket.onopen = () => {
          console.log("WebSocket connection established");
          showNotification("WebSocket connection established", "success");
        };

        websocket.onmessage = (event) => {
          if (typeof event.data === 'string') {
            lastMetadata = event.data.split(':');
          } else if (event.data instanceof ArrayBuffer) {
            if (lastMetadata) {
              const [cameraId, cam_name, person_count, no_helmet_count, no_vest_count, no_gloves_count] = lastMetadata;
              
              // Update the stats from this camera
              const personCount = parseInt(person_count) || 0;
              const noHelmetCount = parseInt(no_helmet_count) || 0;
              const noVestCount = parseInt(no_vest_count) || 0;
              const noGlovesCount = parseInt(no_gloves_count) || 0;
              
              // Convert ArrayBuffer to Blob and create URL
              const blob = new Blob([event.data], { type: 'image/jpeg' });
              const url = URL.createObjectURL(blob);
              
              // Update video feed
              const imgElement = document.getElementById(`video-${cameraId}`);
              if (imgElement) {
                imgElement.src = url;
                
                // Update camera info
                const cameraDetectionCount = document.querySelector(`#camera-${cameraId} + .camera-info .detection-count`);
                const cameraLastDetection = document.querySelector(`#camera-${cameraId} + .camera-info .camera-last-detection`);
                
                if (cameraDetectionCount) {
                  cameraDetectionCount.textContent = `${personCount} people detected`;
                }
                
                if (cameraLastDetection) {
                  cameraLastDetection.textContent = `Last detection: just now`;
                }
                
                // Update status to show we're streaming
                const statusElement = document.getElementById(`stream-status-${cameraId}`);
                if (statusElement) {
                  statusElement.textContent = 'STREAMING';
                  statusElement.classList.remove('not-streaming');
                  statusElement.classList.add('streaming');
                }
              }
              
              // Update global stats
              totalStats.total += personCount;
              totalStats.noHelmet += noHelmetCount;
              totalStats.noVest += noVestCount;
              totalStats.noGloves += noGlovesCount;
              
              // Calculate percentages for the dashboard stats
              const totalPeople = totalStats.total > 0 ? totalStats.total : 1; // Avoid division by zero
              const helmetPercentage = Math.round(((totalPeople - totalStats.noHelmet) / totalPeople) * 100);
              const vestPercentage = Math.round(((totalPeople - totalStats.noVest) / totalPeople) * 100);
              const glovesPercentage = Math.round(((totalPeople - totalStats.noGloves) / totalPeople) * 100);
              
              // Update the stats display
              if (totalDetectionsValue) totalDetectionsValue.textContent = `${totalStats.total}`;
              if (helmetsWornValue) helmetsWornValue.textContent = `${helmetPercentage}%`;
              if (vestsWornValue) vestsWornValue.textContent = `${vestPercentage}%`;
              if (glovesWornValue) glovesWornValue.textContent = `${glovesPercentage}%`;
              
              // Reset metadata after processing
              lastMetadata = null;
            }
          }
        };

        websocket.onerror = (error) => {
          console.error("WebSocket error:", error);
          showNotification("WebSocket connection error", "error");
        };

        websocket.onclose = () => {
          console.log("WebSocket closed");
        };
      }

      // Function to toggle camera list in the modal
      function toggleCameraList() {
        const cameraList = document.getElementById('cameraList');
        if (cameraList) {
          if (cameraList.style.display === 'none') {
            cameraList.style.display = 'block';
            loadCameras();
          } else {
            cameraList.style.display = 'none';
          }
        }
      }

      // Function to load and display the list of cameras
      async function loadCameras() {
        try {
          const response = await fetch("/helmet_detection/get-cameras", {
            method: "GET",
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const cameras = await response.json();
          const cameraList = document.getElementById("cameraList");
          
          if (!cameraList) return;
          
          cameraList.innerHTML = ""; // Clear previous list

          if (Object.keys(cameras).length === 0) {
            cameraList.innerHTML = "<p style='text-align: center; color: #777;'>No cameras added yet</p>";
            return;
          }

          for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
            const cameraItem = document.createElement("div");
            cameraItem.style.padding = "10px";
            cameraItem.style.borderBottom = "1px solid #eee";
            cameraItem.style.display = "flex";
            cameraItem.style.justifyContent = "space-between";
            cameraItem.style.alignItems = "center";
            
            cameraItem.innerHTML = `
              <div>
                <p style="margin: 0; font-weight: 500;">
                  <i class="fas fa-video fa-fw" style="margin-right: 8px; color: var(--primary);"></i>
                  ${cameraName}
                </p>
                <p style="margin: 5px 0 0 0; font-size: 12px; color: #777;">${rtspUrl}</p>
              </div>
              <button onclick="deleteCamera('${cameraName}')" style="background-color: #f44336; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                <i class="fas fa-trash"></i>
              </button>
            `;
            
            cameraList.appendChild(cameraItem);
          }
        } catch (error) {
          console.error("Error loading cameras:", error);
          showNotification("Error loading camera list", "error");
        }
      }

      // Function to delete a camera
      async function deleteCamera(cameraName) {
        const confirmed = confirm(`Are you sure you want to delete the camera "${cameraName}"?`);
        if (!confirmed) return;

        try {
          const response = await fetch(`/helmet_detection/delete-camera/${cameraName}`, {
            method: "DELETE",
          });

          const result = await response.json();

          if (result.status === "success") {
            showNotification(result.message, 'success');
            loadCameras();
            loadCameraCards();
          } else {
            showNotification("Error deleting camera: " + result.message, 'error');
          }
        } catch (error) {
          console.error("Error deleting camera:", error);
          showNotification("Network error while deleting camera", "error");
        }
      }

      // Function to show notification
      function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '15px 20px';
        notification.style.borderRadius = '5px';
        notification.style.color = 'white';
        notification.style.fontSize = '14px';
        notification.style.zIndex = '1100';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.transform = 'translateX(120%)';
        notification.style.transition = 'transform 0.3s ease';
        
        // Set background color based on type
        if (type === 'success') {
          notification.style.backgroundColor = '#2ecc71';
        } else if (type === 'error') {
          notification.style.backgroundColor = '#e74c3c';
        } else if (type === 'warning') {
          notification.style.backgroundColor = '#f39c12';
        } else {
          notification.style.backgroundColor = '#3498db'; // info
        }
        
        // Add icon based on type
        let icon = '';
        if (type === 'success') {
          icon = '<i class="fas fa-check-circle" style="margin-right: 10px; font-size: 16px;"></i>';
        } else if (type === 'error') {
          icon = '<i class="fas fa-exclamation-circle" style="margin-right: 10px; font-size: 16px;"></i>';
        } else if (type === 'warning') {
          icon = '<i class="fas fa-exclamation-triangle" style="margin-right: 10px; font-size: 16px;"></i>';
        } else {
          icon = '<i class="fas fa-info-circle" style="margin-right: 10px; font-size: 16px;"></i>';
        }
        
        notification.innerHTML = icon + message;
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
          notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Hide and remove notification after delay
        setTimeout(() => {
          notification.style.transform = 'translateX(120%)';
          setTimeout(() => {
            notification.remove();
          }, 300);
        }, 3000);
      }

      // Make functions globally available
      window.deleteCamera = deleteCamera;
      window.toggleCameraList = toggleCameraList;

      // Add spinning animation for loading indicators
      const style = document.createElement('style');
      style.textContent = `
        .fa-spin {
          animation: fa-spin 2s infinite linear;
        }
        @keyframes fa-spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `;
      document.head.appendChild(style);

      // When dynamically creating camera cards in JavaScript
      function createCameraCard(cameraId, cameraName) {
        const cameraCard = document.createElement('div');
        cameraCard.className = 'camera-card';
        cameraCard.innerHTML = `
          <div class="camera-feed" id="camera-${cameraId}">
            <img id="video-${cameraId}" />
            <div class="status-badge live-badge">LIVE</div>
            <div class="stream-status not-streaming" id="stream-status-${cameraId}">NOT STREAMING</div>
          </div>
          <div class="camera-info">
            <div class="camera-title">${cameraName}</div>
            <div class="camera-meta">
              <div class="camera-last-detection" id="last-detection-${cameraId}">Last detection: N/A</div>
              <div class="detection-count" id="detection-count-${cameraId}">0 helmets detected</div>
            </div>
            <div class="camera-actions">
              <a href="/helmet_detection/expand?camera=${encodeURIComponent(cameraName)}" class="action-btn expand-btn">Expand</a>
              <button class="action-btn alert-btn">Set Alert</button>
            </div>
          </div>
        `;
        return cameraCard;
      }

  </script>
</body>
</html>