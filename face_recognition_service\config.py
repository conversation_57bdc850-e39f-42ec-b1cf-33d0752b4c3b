import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Face Recognition database settings
    DB_USER: str = "root"
    DB_PASSWORD: str = "Navaneeth%402002"
    DB_HOST: str = "localhost"
    DB_NAME: str = "face_recognition_db"
    DB_PORT: int = 3306

    # Auth service settings
    AUTH_SERVICE_URL: str = "http://localhost:8000"
    
    # Security settings (for token verification)
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    class Config:
        env_file = ".env"

settings = Settings()
