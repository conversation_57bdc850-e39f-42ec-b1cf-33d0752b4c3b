# Simplified face recognition utilities
import cv2
import os
import time
import numpy as np
from scipy.spatial.distance import cosine
from models import Camera
from database import SessionLocal

# Try to import optional dependencies
try:
    from mtcnn import MTCNN
    MTCNN_AVAILABLE = True
except ImportError:
    MTCNN_AVAILABLE = False
    print("Warning: MTCNN not available. Face detection will be limited.")

try:
    from facenet_pytorch import InceptionResnetV1
    import torch
    from torchvision import transforms
    embedder = InceptionResnetV1(pretrained='vggface2').eval()
    FACENET_AVAILABLE = True
except ImportError:
    FACENET_AVAILABLE = False
    print("Warning: FaceNet not available. Face encoding will be limited.")

try:
    import dlib
    from imutils import face_utils
    face_detector = dlib.get_frontal_face_detector()

    # Try to load the landmark predictor
    landmark_predictor = None
    possible_paths = [
        "face_recognition/models/shape_predictor_68_face_landmarks.dat",
        "models/shape_predictor_68_face_landmarks.dat",
        "../models/shape_predictor_68_face_landmarks.dat",
        "../../models/shape_predictor_68_face_landmarks.dat",
        os.path.join(os.path.dirname(__file__), "..", "models", "shape_predictor_68_face_landmarks.dat"),
        os.path.join(os.path.dirname(__file__), "..", "..", "models", "shape_predictor_68_face_landmarks.dat")
    ]

    for path in possible_paths:
        try:
            if os.path.exists(path):
                landmark_predictor = dlib.shape_predictor(path)
                print(f"Successfully loaded landmark predictor from: {path}")
                break
        except Exception as e:
            continue

    if landmark_predictor is None:
        print("Warning: Could not load dlib landmark predictor. Face alignment will be disabled.")
        print("Please download shape_predictor_68_face_landmarks.dat from:")
        print("http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2")
        print("Extract and place it in the face_recognition/models/ directory")

    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    face_detector = None
    landmark_predictor = None
    print("Warning: dlib not available. Advanced face alignment will be disabled.")

def generate_encoding(face_image):
    """Generates a face encoding using PyTorch FaceNet with face alignment."""
    if not FACENET_AVAILABLE:
        print("Warning: FaceNet not available. Returning dummy encoding.")
        return np.random.rand(512)  # Return dummy encoding

    try:
        # Apply face alignment
        aligned_face = align_face(face_image)

        # Convert BGR (OpenCV) to RGB
        aligned_face = cv2.cvtColor(aligned_face, cv2.COLOR_BGR2RGB)

        # Resize and preprocess the image (alignment already resizes to 160x160)
        if aligned_face.shape[0] != 160 or aligned_face.shape[1] != 160:
            aligned_face = cv2.resize(aligned_face, (160, 160))

        # Convert to tensor and normalize
        preprocess = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

        # Convert to tensor
        face_tensor = preprocess(aligned_face).unsqueeze(0)

        # Generate embedding
        with torch.no_grad():
            embedding = embedder(face_tensor).cpu().numpy().flatten()

        return embedding
    except Exception as e:
        print(f"Error generating encoding: {e}")
        return np.random.rand(512)  # Return dummy encoding on error

def generate_deepface_encoding(face_image):
        """Generates a face encoding using PyTorch FaceNet (alias for generate_encoding)."""
        return generate_encoding(face_image)


def align_face(image):
    """
    Align a face image using dlib's facial landmarks.

    Args:
        image: Input face image (numpy.ndarray)

    Returns:
        Aligned face image or original image if alignment fails
    """
    # If dlib is not available, return original image
    if not DLIB_AVAILABLE or landmark_predictor is None:
        # Just resize the image to 160x160 and return
        try:
            return cv2.resize(image, (160, 160))
        except Exception:
            return image

    try:
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect faces
        faces = face_detector(gray, 0)

        if len(faces) == 0:
            # No face detected, just resize and return
            return cv2.resize(image, (160, 160))

        # Get the first face
        face = faces[0]

        # Detect landmarks
        shape = landmark_predictor(gray, face)
        shape = face_utils.shape_to_np(shape)

        # Extract the left and right eye coordinates
        left_eye = shape[36:42]  # Left eye landmarks
        right_eye = shape[42:48]  # Right eye landmarks

        # Calculate the center of each eye
        left_eye_center = left_eye.mean(axis=0).astype("int")
        right_eye_center = right_eye.mean(axis=0).astype("int")

        # Calculate the angle between the eye centers
        dy = right_eye_center[1] - left_eye_center[1]
        dx = right_eye_center[0] - left_eye_center[0]
        angle = np.degrees(np.arctan2(dy, dx))

        # Calculate the desired right eye position
        desired_dist = 0.3  # Proportion of the image width for eye distance
        desired_size = (160, 160)  # Final size of the aligned face

        # Calculate scale based on the distance between eyes
        eye_dist = np.sqrt((dx ** 2) + (dy ** 2))
        desired_eye_dist = desired_dist * desired_size[0]
        scale = desired_eye_dist / eye_dist

        # Calculate the center point between the eyes
        eyes_center = ((left_eye_center[0] + right_eye_center[0]) // 2,
                      (left_eye_center[1] + right_eye_center[1]) // 2)

        # Get the rotation matrix
        M = cv2.getRotationMatrix2D(eyes_center, angle, scale)

        # Update the translation component of the matrix
        tx = desired_size[0] * 0.5
        ty = desired_size[1] * 0.35  # Position eyes at 35% from the top
        M[0, 2] += (tx - eyes_center[0])
        M[1, 2] += (ty - eyes_center[1])

        # Apply the affine transformation
        aligned_face = cv2.warpAffine(image, M, desired_size, flags=cv2.INTER_CUBIC)

        return aligned_face

    except Exception as e:
        print(f"Face alignment error: {e}")
        # On error, just resize the image to 160x160 and return
        try:
            return cv2.resize(image, (160, 160))
        except Exception:
            return image

def crop_face(image_input):
    """
    Crop the face from an image.

    Args:
        image_input: Either a file path (string) or an image array (numpy.ndarray)

    Returns:
        The cropped face image or None if no face is detected
    """
    # Check if input is a file path or an image array
    if isinstance(image_input, str):
        image = cv2.imread(image_input)
    else:
        image = image_input

    # Initialize the MTCNN detector
    detector = MTCNN()

    # Detect faces
    faces = detector.detect_faces(image)

    # Check if any faces were detected
    if not faces:
        return None

    # Get the first face
    face = faces[0]
    x, y, w, h = face['box']

    # Ensure coordinates are valid (not negative)
    x = max(0, x)
    y = max(0, y)

    # Crop the face
    cropped_face = image[y:y+h, x:x+w]
    return cropped_face

def calculate_cosine_similarity(embedding1, embedding2):
    return 1-cosine(embedding1, embedding2)

def load_cameras(db=None):
    """
    Load cameras from the database.

    Args:
        db: SQLAlchemy database session

    Returns:
        Dictionary of cameras with camera name as key and a list containing RTSP URL as value
    """
    from database import SessionLocal

    # If no db session is provided, create one
    if db is None:
        db_session = SessionLocal()
        close_session = True
    else:
        db_session = db
        close_session = False

    try:
        # Query all cameras from the database
        cameras = db_session.query(Camera).all()

        # Convert to the expected format (dict with camera name as key and list with RTSP URL as value)
        result = {}
        for camera in cameras:
            result[camera.name] = [camera.rtsp_url]
        return result
    finally:
        # Close the session if we created it
        if close_session:
            db_session.close()

# This function is kept for backward compatibility but doesn't do anything
# since we're now using the database
def save_cameras(cameras_data):
    """
    This function is deprecated. Cameras should be saved to the database using the API endpoints.
    """
    print("Warning: save_cameras() is deprecated. Use the API endpoints to manage cameras.")

def get_username_from_id(user_id, db_users_cache):
    """Function to extract the username from the user_id from cached data of user."""
    user = next((user for user in db_users_cache if user.id == user_id), None)
    if user:
        return user.username
    return None

# to save images with a delay mechanism
# This is to prevent saving too many images in a short time

last_saved_time = {}
SAVE_DELAY = 10  # seconds

def save_person_image(face_image, username):
    """Save face image to per-user folder with a delay mechanism."""
    global last_saved_time

    # Validate face image before saving
    if face_image is None or face_image.size == 0:
        print(f"[INFO] Skipped saving invalid image for {username}")
        return False

    # Add minimum face size check
    if face_image.shape[0] < 50 or face_image.shape[1] < 50:
        print(f"[INFO] Skipped saving too small face for {username}")
        return False

    current_time = time.time()
    user_folder = os.path.join("Dataset", username)
    os.makedirs(user_folder, exist_ok=True)

    last_time = last_saved_time.get(username, 0)
    if current_time - last_time >= SAVE_DELAY:
        timestamp = int(current_time)
        image_path = os.path.join(user_folder, f"{username}_{timestamp}.jpg")
        cv2.imwrite(image_path, face_image)
        last_saved_time[username] = current_time
        print(f"[INFO] Saved image for {username} at {image_path}")
        return True
    else:
        print(f"[INFO] Skipped saving image for {username} (within delay window)")
        return False

def save_unknown_image(face_image, persistent_id):
    """Save unknown face image with delay based on persistent_id.
    Returns the image path if successful, None otherwise."""
    global last_saved_time

    current_time = time.time()
    folder_path = os.path.join("Dataset", "unknown")
    os.makedirs(folder_path, exist_ok=True)

    # Ensure the face image is valid
    if face_image is None or face_image.size == 0:
        print(f"[ERROR] Invalid face image for {persistent_id}")
        return None

    # Ensure the persistent_id is valid
    if not persistent_id or not isinstance(persistent_id, str):
        print(f"[ERROR] Invalid persistent_id: {persistent_id}")
        return None

    # Generate timestamp and image filename
    timestamp = int(current_time)
    image_filename = f"unknown_{persistent_id}_{timestamp}.jpg"
    image_path = os.path.join(folder_path, image_filename)
    web_path = f"Dataset/unknown/{image_filename}"  # Path for web access

    # Check if we should save based on delay
    last_time = last_saved_time.get(persistent_id, 0)
    if current_time - last_time >= SAVE_DELAY:
        try:
            success = cv2.imwrite(image_path, face_image)
            if success:
                last_saved_time[persistent_id] = current_time
                print(f"[INFO] Saved unknown image for {persistent_id} at {image_path}")
                return web_path  # Return the web-accessible path
            else:
                print(f"[ERROR] Failed to save image for {persistent_id} at {image_path}")
                return None
        except Exception as e:
            print(f"[ERROR] Exception saving image for {persistent_id}: {str(e)}")
            return None
    else:
        print(f"[INFO] Skipped saving image for {persistent_id} (within delay window)")
        return None
