from fastapi import APIRouter, WebSocket, Request, HTTPException, UploadFile, File, Depends
from typing import Optional
from fastapi.templating import Jinja2Templates
from utils import HelmetDetection
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
import cv2
import shutil
import os
from sqlalchemy.orm import Session
from database import get_db
from models import Detection, Alert, Camera, DetectionCreate, AlertCreate, CameraCreate

router = APIRouter()

templates = Jinja2Templates(directory="helmet_detection/templates")

@router.get("/helmet_detection")
async def show_helmet_detection_page(request: Request):
    # Get user role from request state (set by middleware)
    user_role = getattr(request.state, "user", {}).get("role", "user")
    return templates.TemplateResponse("helmet_detection.html", {"request": request, "user_role": user_role})

# Initialize Helmet detection without starting it
helmet_stream = None

def get_helmet_stream():
    global helmet_stream
    if helmet_stream is None:
        helmet_stream = HelmetDetection(
            object_model="./helmet_detection/models/seg-modelv2.engine",
            pose_model="./helmet_detection/models/yolov8x-pose.engine",
            conf_value=0.5
        )
    return helmet_stream

def shutdown_event():
    global helmet_stream
    if helmet_stream:
        helmet_stream.stop()

# API to start video processing
@router.post("/start-helmet_detection")
async def start_stream(request: Request, file: Optional[UploadFile] = File(None), db: Session = Depends(get_db)):
    helmet_detector = get_helmet_stream()
    
    if not helmet_detector.running:
        camera_details = load_cameras(db)
        test_video_path = None

        if file:
            save_path = f"./helmet_detection/uploads/{file.filename}"
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            test_video_path = save_path

        helmet_detector.start(camera_details, test_video_path=test_video_path)

    return {"status": "helmet detection started"}

# API to stop video processing
@router.post("/stop-stream")
async def stop_stream():
    helmet_detector = get_helmet_stream()
    if helmet_detector.running:
        helmet_detector.stop()
        if hasattr(helmet_detector, 'helmet_frames'):
            helmet_detector.helmet_frames = []
    return {"status": "helmet detection stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            helmet_detector = get_helmet_stream()
            if helmet_detector and helmet_detector.running:
                if hasattr(helmet_detector, 'helmet_frames'):
                    for idx in range(len(helmet_detector.helmet_frames)):
                        if helmet_detector.helmet_frames[idx] is not None:
                            warnings = helmet_detector.warning_count[idx] if hasattr(helmet_detector, 'warning_count') else {}
                            cam_name = helmet_detector.camera_name[idx] if hasattr(helmet_detector, 'camera_name') else f"Camera {idx}"
                            frame = helmet_detector.helmet_frames[idx]
                            
                            person_count = warnings.get('person_count', 0)
                            no_helmet_count = warnings.get('no_helmet_count', 0)
                            no_vest_count = warnings.get('no_vest_count', 0)
                            no_gloves_count = warnings.get('no_gloves_count', 0)
                            
                            await websocket.send_text(f"{idx}:{cam_name}:{person_count}:{no_helmet_count}:{no_vest_count}:{no_gloves_count}")
                            await websocket.send_bytes(frame)
                            
                await asyncio.sleep(0.03)
            else:
                cv2.destroyAllWindows()
                await asyncio.sleep(0.5)  # Avoid busy waiting
    except Exception as e:
        print("WebSocket disconnected:", e)

# Database-based camera management
def load_cameras(db: Session):
    """Load cameras from database"""
    cameras = db.query(Camera).filter(Camera.is_active == True).all()
    result = {}
    for camera in cameras:
        result[camera.name] = [camera.rtsp_url, []]  # Empty ROI for now
    return result

def save_camera_to_db(db: Session, camera_data: CameraCreate):
    """Save camera to database"""
    db_camera = Camera(
        name=camera_data.name,
        rtsp_url=camera_data.rtsp_url,
        is_active=True
    )
    db.add(db_camera)
    db.commit()
    db.refresh(db_camera)
    return db_camera

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData, db: Session = Depends(get_db)):
    # Check if the camera name already exists
    existing_camera = db.query(Camera).filter(Camera.name == camera_data.cameraName).first()
    if existing_camera:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists
    existing_url = db.query(Camera).filter(Camera.rtsp_url == camera_data.rtspUrl).first()
    if existing_url:
        return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_url.name}"}

    # Add new camera
    try:
        new_camera = Camera(
            name=camera_data.cameraName,
            rtsp_url=camera_data.rtspUrl,
            is_active=True
        )
        db.add(new_camera)
        db.commit()
        return {"status": "success", "message": "Camera added successfully"}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": f"Failed to add camera: {str(e)}"}

# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str, db: Session = Depends(get_db)):
    camera = db.query(Camera).filter(Camera.name == camera_name).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Soft delete - mark as inactive
    camera.is_active = False
    db.commit()
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}

@router.get("/get-cameras")
async def get_cameras(db: Session = Depends(get_db)):
    cameras = load_cameras(db)
    return cameras

# Detection endpoints
@router.get("/detections")
async def get_detections(db: Session = Depends(get_db)):
    """Get recent detections"""
    detections = db.query(Detection).order_by(Detection.timestamp.desc()).limit(100).all()
    return detections

@router.get("/alerts")
async def get_alerts(db: Session = Depends(get_db)):
    """Get recent alerts"""
    alerts = db.query(Alert).order_by(Alert.timestamp.desc()).limit(50).all()
    return alerts

@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: int, db: Session = Depends(get_db)):
    """Mark an alert as resolved"""
    alert = db.query(Alert).filter(Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="Alert not found")
    
    alert.is_resolved = True
    alert.resolved_at = datetime.now(timezone.utc)
    db.commit()
    
    return {"status": "success", "message": "Alert resolved"}

# Statistics endpoint
@router.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get detection statistics"""
    from datetime import datetime, timedelta
    
    # Get stats for the last 24 hours
    yesterday = datetime.now() - timedelta(days=1)
    
    total_detections = db.query(Detection).filter(Detection.timestamp >= yesterday).count()
    helmet_detections = db.query(Detection).filter(
        Detection.timestamp >= yesterday,
        Detection.detection_type == 'helmet'
    ).count()
    no_helmet_detections = db.query(Detection).filter(
        Detection.timestamp >= yesterday,
        Detection.detection_type == 'no_helmet'
    ).count()
    active_alerts = db.query(Alert).filter(Alert.is_resolved == False).count()
    
    return {
        "total_detections": total_detections,
        "helmet_detections": helmet_detections,
        "no_helmet_detections": no_helmet_detections,
        "active_alerts": active_alerts
    }
