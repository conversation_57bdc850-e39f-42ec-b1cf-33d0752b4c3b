<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - Camera Expanded View</title>
  <link rel="stylesheet" href="{{ url_for('crowd_detection_static', path='crowd_detection.css') }}">
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
      --panel-bg: #ffffff;
      --border-radius: 8px;
      --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
      --primary-light: rgba(27, 165, 123, 0.1);
      --primary-color: #1ba57b;
      --transition: all 0.3s ease;
      --text-muted: #7a7a7a;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }

    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto;
    }

    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
    }

    .logo svg {
      margin-right: 12px;
    }

    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
    }

    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }

    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }

    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }

    .system-status {
      margin-top: auto;
      padding: 15px 25px;
      font-size: 13px;
      background-color: rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #4caf50;
    }

    .status-badge {
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-active {
      background-color: rgba(46, 125, 50, 0.1);
      color: #2e7d32;
    }

    .status-offline {
      background-color: rgba(211, 47, 47, 0.1);
      color: #d32f2f;
    }

    .status-connecting {
      background-color: rgba(255, 152, 0, 0.1);
      color: #ff9800;
    }

    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      margin-left: 280px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }

    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }

    .btn svg {
      margin-right: 8px;
    }

    .btn-primary {
      background-color: #1565c0;
      color: white;
    }

    .btn-primary:hover {
      background-color: #d01736;
    }

    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }

    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }

    .content-area {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
      flex: 1;
    }

    .panel {
      background-color: var(--panel-bg);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-sm);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #eaeaea;
    }

    .panel-title {
      font-size: 18px;
      font-weight: 500;
    }

    .panel-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--primary-light);
      color: var(--primary-color);
      cursor: pointer;
      transition: var(--transition);
    }

    .action-btn:hover {
      background-color: rgba(27, 165, 123, 0.2);
    }

    .panel-body {
      padding: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    /* Webcam view */
    .webcam-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #0d1b42;
      border-radius: 4px;
      min-height: 400px;
      overflow: hidden; /* Prevent image overflow */
    }

    .webcam-placeholder {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--text-muted);
    }

    .webcam-placeholder i {
      font-size: 40px;
      margin-bottom: 16px;
    }

    .camera-controls {
      margin-top: 16px;
      display: flex;
      gap: 12px;
    }

    /* Crowd metrics */
    .crowd-metrics {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .metric-card {
      background: #fff;
      border-radius: 8px;
      padding: 14px 16px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
    }

    .metric-card:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .metric-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray);
    }

    .metric-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-value {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 4px;
      color: var(--dark);
    }

    .metric-subtitle {
      font-size: 13px;
      color: var(--text-muted);
    }

    .metric-counts {
      display: flex;
      margin-top: 10px;
      gap: 15px;
    }

    .count-item {
      flex: 1;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
      text-align: center;
    }

    .count-label {
      font-size: 12px;
      color: var(--text-muted);
      margin-bottom: 4px;
    }

    .count-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--dark);
    }

    .green-count {
      color: #2e7d32;
    }

    .amber-count {
      color: #ff9800;
    }

    .red-count {
      color: #d32f2f;
    }

    @media (max-width: 1024px) {
      .content-area {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      body {
        flex-direction: column;
      }

      .sidebar {
        width: 100%;
        height: auto;
        position: relative;
      }

      .content {
        margin-left: 0;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>
    <a href="/" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>
    <a href="/crowd_detection" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
      Crowd Detection
    </a>
    <a href="/crowd_detection/addroi" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 20h9"></path>
        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
      </svg>
      Set Focus Area
    </a>
  </div>

  <div class="content">
    <div class="header">
      <div class="page-title" id="camera-title">
        Camera Expanded View
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline" id="backBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Back to Dashboard
        </button>
        <button class="btn btn-primary" id="setRoiBtn">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 20h9"></path>
            <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
          </svg>
          <span id="setRoiBtnText">Set Focus Area</span>
        </button>
      </div>
    </div>

    <div class="content-area">
      <!-- Webcam View Panel -->
      <div class="panel">
        <div class="panel-header">
          <h2 class="panel-title">Camera Feed</h2>
        </div>
        <div class="panel-body">
          <div class="webcam-container" id="webcam-container" style="position: relative; width: 100%; height: 100%;">
            <!-- Regular camera feed -->
            <img id="camera-feed" src="" alt="Camera Feed" style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1;">

            <!-- ROI Drawing Layer - initially hidden -->
            <div id="roi-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 10; display: none;">
              <!-- Canvas for drawing ROI polygons -->
              <canvas id="roi-canvas" width="800" height="600" style="width: 100%; height: 100%; cursor: crosshair;"></canvas>

              <!-- Drawing controls -->
              <div style="position: absolute; top: 10px; right: 10px; z-index: 20; display: flex; gap: 10px;">
                <button id="save-roi-btn" class="btn btn-success">Save</button>
                <button id="clear-roi-btn" class="btn btn-danger">Clear All</button>
                <button id="close-polygon-btn" class="btn btn-primary">Close Polygon</button>
                <button id="cancel-roi-btn" class="btn btn-secondary">Cancel</button>
              </div>

              <!-- Drawing instructions -->
              <div id="drawing-instructions" style="position: absolute; bottom: 10px; left: 10px; background-color: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; z-index: 20;">
                <p style="margin: 0; font-size: 14px;"><strong>Drawing Mode:</strong> Click to place points. Double-click or use the "Close Polygon" button to complete a polygon.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Camera Details Panel -->
      <div class="panel">
        <div class="panel-header">
          <h2 class="panel-title">Camera Details</h2>
          <div class="panel-actions">
            <div class="status-badge status-active" id="camera-status">
              Live
            </div>
          </div>
        </div>
        <div class="panel-body">
          <div class="crowd-metrics">
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">Camera Name</div>
                <div class="metric-icon blue-bg">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="blue-icon">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                    <circle cx="12" cy="13" r="4"></circle>
                  </svg>
                </div>
              </div>
              <div class="metric-value" id="camera-name">Loading...</div>
              <div class="metric-subtitle" id="last-updated">Updated just now</div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">People Count</div>
                <div class="metric-icon orange-bg">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="orange-icon">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
              </div>
              <div class="metric-value" id="people-count">0</div>
              <div class="metric-subtitle">Total people detected</div>

              <div class="metric-counts" id="roi-counts">
                <!-- ROI counts will be added dynamically -->
              </div>
            </div>

            <div style="margin-top: 15px; text-align: center;">
              <button class="btn btn-primary" id="setAlertBtn" style="width: 100%;">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
                Configure Alerts
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Debug HTML structure
      console.log("DOM Content Loaded");
      console.log("ROI Container HTML:", document.getElementById('roi-container') ? document.getElementById('roi-container').outerHTML : "Not found");
      console.log("Webcam Container HTML:", document.getElementById('webcam-container') ? document.getElementById('webcam-container').outerHTML : "Not found");
      const backBtn = document.getElementById('backBtn');
      const setRoiBtn = document.getElementById('setRoiBtn');
      const setAlertBtn = document.getElementById('setAlertBtn');
      const cameraTitle = document.getElementById('camera-title');
      const cameraName = document.getElementById('camera-name');
      const peopleCount = document.getElementById('people-count');
      const lastUpdated = document.getElementById('last-updated');
      const roiCounts = document.getElementById('roi-counts');
      const cameraFeed = document.getElementById('camera-feed');
      const cameraStatus = document.getElementById('camera-status');
      const webcamContainer = document.getElementById('webcam-container');

      // Add a listener for monitoring state changes
      window.addEventListener('storage', function(event) {
        if (event.key === 'crowdMonitoringActive') {
          console.log("Monitoring state changed:", event.newValue);

          if (event.newValue === 'false') {
            // Monitoring was stopped from another page
            console.log("Monitoring stopped from another page");
            resetCameraFeedToOffline();
          } else if (event.newValue === 'true') {
            // Monitoring was started from another page
            console.log("Monitoring started from another page");
            window.location.reload(); // Reload to start the WebSocket connection
          }
        }
      });

      // Get camera data from sessionStorage, localStorage, and server-side variables
      const sessionCameraData = JSON.parse(sessionStorage.getItem('selectedCamera') || '{}');
      const storedCameraData = JSON.parse(localStorage.getItem('crowdCameraData') || '{}');

      // Use server-side variables if available, otherwise fall back to URL parameters or sessionStorage
      const cameraId = '{{ camera_id }}' || sessionCameraData.id || new URLSearchParams(window.location.search).get('id');
      const serverCameraName = '{{ camera_name }}';

      // Check if monitoring is active from localStorage
      const isMonitoring = localStorage.getItem('crowdMonitoringActive') === 'true';

      console.log("Camera ID:", cameraId);
      console.log("Monitoring state:", isMonitoring);
      console.log("Stored camera data:", storedCameraData);

      // Update UI with camera data
      if (serverCameraName && serverCameraName !== '') {
        cameraTitle.textContent = `Camera: ${decodeURIComponent(serverCameraName)}`;
        cameraName.textContent = decodeURIComponent(serverCameraName);
      } else if (cameraData.name) {
        cameraTitle.textContent = `Camera: ${cameraData.name}`;
        cameraName.textContent = cameraData.name;
      } else {
        // Try to get camera name from URL parameter
        const urlCameraName = new URLSearchParams(window.location.search).get('name');
        if (urlCameraName) {
          cameraTitle.textContent = `Camera: ${decodeURIComponent(urlCameraName)}`;
          cameraName.textContent = decodeURIComponent(urlCameraName);
        }
      }

      console.log("Camera ID:", cameraId);
      console.log("Camera Name:", cameraName.textContent);

      // Check if monitoring is active
      if (isMonitoring) {
        console.log("Monitoring is active, setting up WebSocket connection");
        // Set up the WebSocket connection to get live updates
        setupWebSocket(cameraId);

        // Get camera data from localStorage if available
        const cameraInfo = storedCameraData[cameraId];

        // Update UI with camera data if available
        if (cameraInfo && Object.keys(cameraInfo).length > 0) {
          console.log("Using camera data from localStorage:", cameraInfo);
          peopleCount.textContent = cameraInfo.count || '0';

          // Add ROI counts if available
          if (cameraInfo.roiCounts && cameraInfo.roiCounts.length > 0) {
            roiCounts.innerHTML = '';
            cameraInfo.roiCounts.forEach((count, index) => {
              const countClass = count > 10 ? 'red-count' : count > 5 ? 'amber-count' : 'green-count';
              roiCounts.innerHTML += `
                <div class="count-item">
                  <div class="count-label">Region ${index + 1}</div>
                  <div class="count-value ${countClass}">${count}</div>
                </div>
              `;
            });
          }

          // Format last updated time
          if (cameraInfo.lastUpdate) {
            const lastUpdateTime = new Date(cameraInfo.lastUpdate);
            lastUpdated.textContent = `Updated ${formatTimeAgo(lastUpdateTime)}`;
          }
        } else {
          // If no data is available, show initial state
          peopleCount.textContent = '0';
          lastUpdated.textContent = 'Waiting for data...';

          // Show placeholder until we get data
          webcamContainer.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #f0f0f0;">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                <circle cx="12" cy="13" r="4"></circle>
              </svg>
              <h3 style="margin-top: 20px; color: #333;">Connecting to camera...</h3>
              <p style="margin-top: 10px; color: #666;">Please wait while we establish the connection.</p>
            </div>
          `;

          // Update status badge
          cameraStatus.textContent = 'Connecting';
          cameraStatus.className = 'status-badge status-connecting';
        }
      } else {
        console.log("Monitoring is not active");
        // Show message that monitoring is not active
        webcamContainer.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #f0f0f0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ff9800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
              <circle cx="12" cy="13" r="4"></circle>
            </svg>
            <h3 style="margin-top: 20px; color: #333;">Monitoring not active</h3>
            <p style="margin-top: 10px; color: #666;">Please start monitoring from the dashboard to view the camera feed.</p>
            <button id="startMonitoringBtn" style="margin-top: 20px; padding: 10px 20px; background-color: #1e88e5; color: white; border: none; border-radius: 4px; cursor: pointer;">Start Monitoring</button>
          </div>
        `;

        // Update status badge
        cameraStatus.textContent = 'Offline';
        cameraStatus.className = 'status-badge status-offline';

        // Add event listener to the Start Monitoring button
        document.getElementById('startMonitoringBtn').addEventListener('click', async function() {
          try {
            const response = await fetch('/crowd_detection/start-stream', { method: 'POST' });
            if (response.ok) {
              console.log("Stream started successfully from expanded view");
              // Store monitoring state in localStorage
              localStorage.setItem('crowdMonitoringActive', 'true');
              // Reload the page to apply the new monitoring state
              window.location.reload();
            } else {
              console.error("Failed to start stream");
              alert("Failed to start monitoring. Please try again.");
            }
          } catch (error) {
            console.error("Error starting stream:", error);
            alert("Error starting monitoring. Please try again.");
          }
        });
      }

      // Event listeners
      backBtn.addEventListener('click', function() {
        console.log("Navigating back to dashboard");
        // The router is mounted at /crowd_detection, so we need to use the correct path
        window.location.href = '/crowd_detection/crowd_detection';
      });

      // ROI Drawing Variables
      let roiDrawingMode = false;
      let isDrawing = false;
      let polygons = [];
      let currentPolygon = [];

      // ROI Drawing Elements - with debugging
      console.log("Initializing ROI drawing elements");
      const roiContainer = document.getElementById('roi-container');
      console.log("ROI Container:", roiContainer);

      const roiVideo = document.getElementById('roi-video');
      console.log("ROI Video:", roiVideo);

      const roiCanvas = document.getElementById('roi-canvas');
      console.log("ROI Canvas:", roiCanvas);

      const saveRoiBtn = document.getElementById('save-roi-btn');
      console.log("Save ROI Button:", saveRoiBtn);

      const clearRoiBtn = document.getElementById('clear-roi-btn');
      console.log("Clear ROI Button:", clearRoiBtn);

      const cancelRoiBtn = document.getElementById('cancel-roi-btn');
      console.log("Cancel ROI Button:", cancelRoiBtn);

      // Initialize canvas context
      let ctx = roiCanvas ? roiCanvas.getContext('2d') : null;
      console.log("Canvas Context:", ctx);

      // Function to resize canvas to match container
      function resizeRoiCanvas() {
        if (!roiCanvas || !roiContainer) return;

        const rect = roiContainer.getBoundingClientRect();
        roiCanvas.width = rect.width;
        roiCanvas.height = rect.height;

        console.log(`Canvas resized to ${roiCanvas.width}x${roiCanvas.height}`);

        // Redraw polygons after resize
        drawPolygons();
      }

      // Toggle ROI drawing mode
      setRoiBtn.addEventListener('click', function() {
        console.log("Set ROI button clicked");

        try {
          const currentCameraId = cameraId;
          const currentCameraName = cameraName.textContent;

          console.log("Entering ROI drawing mode for camera:", currentCameraId, currentCameraName);

          // Get elements
          const roiContainer = document.getElementById('roi-container');
          const roiCanvas = document.getElementById('roi-canvas');

          console.log("ROI Container:", roiContainer);
          console.log("ROI Canvas:", roiCanvas);

          // Show the ROI container
          if (roiContainer) {
            roiContainer.style.display = 'block';
            console.log("ROI container shown");
          } else {
            console.error("ROI container element not found");
            return;
          }

          // Initialize canvas context
          if (roiCanvas) {
            // Get the canvas context
            ctx = roiCanvas.getContext('2d');
            console.log("Canvas context:", ctx);

            // Set canvas dimensions to match container
            const rect = roiContainer.getBoundingClientRect();
            roiCanvas.width = rect.width;
            roiCanvas.height = rect.height;
            console.log("Canvas dimensions set to:", roiCanvas.width, "x", roiCanvas.height);

            // Add a direct click handler to the canvas for testing
            roiCanvas.addEventListener('click', function(e) {
              console.log("Direct canvas click detected at:", e.clientX, e.clientY);

              // Get position relative to canvas
              const rect = roiCanvas.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;
              console.log("Click position relative to canvas:", x, y);

              // Draw a red circle at the click position
              if (ctx) {
                ctx.beginPath();
                ctx.arc(x, y, 10, 0, Math.PI * 2);
                ctx.fillStyle = "red";
                ctx.fill();

                // Add point to current polygon
                currentPolygon.push([x, y]);
                console.log("Point added to polygon at", x, y);
                console.log("Current polygon now has", currentPolygon.length, "points");

                // Draw polygons
                drawPolygons();
              }
            });
            console.log("Direct canvas click handler added");
          } else {
            console.error("ROI Canvas element not found");
            return;
          }

          // Set up event listeners for drawing
          console.log("Setting up event listeners...");
          setupRoiEventListeners();

          // Load existing ROI coordinates if available
          console.log("Loading existing ROI coordinates...");
          loadExistingROI(currentCameraName);

          // Set drawing mode flag
          roiDrawingMode = true;
          console.log("Drawing mode enabled");
        } catch (error) {
          console.error("Error in Set ROI button click handler:", error);
        }
      });

      setAlertBtn.addEventListener('click', function() {
        alert('Alert configuration will be implemented in a future update.');
      });
    });

    // Set up ROI drawing event listeners
    function setupRoiEventListeners() {
      console.log("Setting up ROI drawing event listeners");

      try {
        // Get elements
        const roiCanvas = document.getElementById('roi-canvas');
        const saveRoiBtn = document.getElementById('save-roi-btn');
        const clearRoiBtn = document.getElementById('clear-roi-btn');
        const cancelRoiBtn = document.getElementById('cancel-roi-btn');

        console.log("Elements for event listeners:", {
          roiCanvas,
          saveRoiBtn,
          clearRoiBtn,
          cancelRoiBtn
        });

        // Check if elements exist
        if (!roiCanvas) {
          console.error("ROI Canvas element not found in setupRoiEventListeners");
          return;
        }

        if (!saveRoiBtn) {
          console.error("Save ROI Button element not found in setupRoiEventListeners");
          return;
        }

        if (!clearRoiBtn) {
          console.error("Clear ROI Button element not found in setupRoiEventListeners");
          return;
        }

        if (!cancelRoiBtn) {
          console.error("Cancel ROI Button element not found in setupRoiEventListeners");
          return;
        }

        // Canvas event listeners for drawing
        console.log("Adding canvas event listeners");
        roiCanvas.addEventListener('mousedown', handleRoiMouseDown);
        roiCanvas.addEventListener('mouseup', handleRoiMouseUp);
        roiCanvas.addEventListener('mousemove', handleRoiMouseMove);
        roiCanvas.addEventListener('dblclick', handleRoiDoubleClick);

        // Window resize listener
        console.log("Adding window resize listener");
        window.addEventListener('resize', function() {
          const roiContainer = document.getElementById('roi-container');
          if (roiContainer && roiContainer.style.display !== 'none') {
            const rect = roiContainer.getBoundingClientRect();
            roiCanvas.width = rect.width;
            roiCanvas.height = rect.height;
            console.log("Canvas resized on window resize:", roiCanvas.width, "x", roiCanvas.height);
            drawPolygons();
          }
        });

        // Button event listeners
        console.log("Adding button event listeners");
        saveRoiBtn.addEventListener('click', saveRoiCoordinates);
        clearRoiBtn.addEventListener('click', clearRoiPolygons);

        // Close polygon button
        const closePolygonBtn = document.getElementById('close-polygon-btn');
        if (closePolygonBtn) {
          closePolygonBtn.addEventListener('click', function() {
            console.log("Close polygon button clicked");
            if (currentPolygon.length > 2) {
              // Close the polygon
              currentPolygon.push(currentPolygon[0]);

              // Add to list of polygons
              polygons.push([...currentPolygon]);
              console.log("Polygon closed manually with", currentPolygon.length, "points");

              // Reset for next polygon
              currentPolygon = [];

              // Redraw polygons
              drawPolygons();
            } else {
              console.log("Not enough points to close polygon, need at least 3 points");
              alert("Please add at least 3 points to create a polygon");
            }
          });
          console.log("Close polygon button event listener added");
        }

        cancelRoiBtn.addEventListener('click', exitRoiDrawingMode);

        console.log("ROI drawing event listeners set up successfully");
      } catch (error) {
        console.error("Error in setupRoiEventListeners:", error);
      }
    }

    // Remove ROI drawing event listeners
    function removeRoiEventListeners() {
      console.log("Removing ROI drawing event listeners");

      try {
        // Get elements
        const roiCanvas = document.getElementById('roi-canvas');
        const saveRoiBtn = document.getElementById('save-roi-btn');
        const clearRoiBtn = document.getElementById('clear-roi-btn');
        const cancelRoiBtn = document.getElementById('cancel-roi-btn');

        // Remove canvas event listeners
        if (roiCanvas) {
          roiCanvas.removeEventListener('mousedown', handleRoiMouseDown);
          roiCanvas.removeEventListener('mouseup', handleRoiMouseUp);
          roiCanvas.removeEventListener('mousemove', handleRoiMouseMove);
          roiCanvas.removeEventListener('dblclick', handleRoiDoubleClick);
          roiCanvas.removeEventListener('click', null); // Remove direct click handler
          console.log("Canvas event listeners removed");
        }

        // Remove button event listeners
        if (saveRoiBtn) {
          saveRoiBtn.removeEventListener('click', saveRoiCoordinates);
        }

        if (clearRoiBtn) {
          clearRoiBtn.removeEventListener('click', clearRoiPolygons);
        }

        // Remove close polygon button event listener
        const closePolygonBtn = document.getElementById('close-polygon-btn');
        if (closePolygonBtn) {
          closePolygonBtn.removeEventListener('click', null);
        }

        if (cancelRoiBtn) {
          cancelRoiBtn.removeEventListener('click', exitRoiDrawingMode);
        }

        console.log("ROI drawing event listeners removed successfully");
      } catch (error) {
        console.error("Error in removeRoiEventListeners:", error);
      }
    }

    // Exit ROI drawing mode
    function exitRoiDrawingMode() {
      console.log("Exiting ROI drawing mode");

      try {
        // Get elements
        const roiContainer = document.getElementById('roi-container');

        // Hide ROI container
        if (roiContainer) {
          roiContainer.style.display = 'none';
          console.log("ROI container hidden");
        } else {
          console.error("ROI container element not found");
        }

        // Remove event listeners
        removeRoiEventListeners();

        // Reset drawing state
        roiDrawingMode = false;
        isDrawing = false;
        currentPolygon = [];
        console.log("Drawing state reset");
      } catch (error) {
        console.error("Error in exitRoiDrawingMode:", error);
      }
    }

    // ROI Mouse down handler
    function handleRoiMouseDown(event) {
      console.log("ROI canvas mousedown event received:", event);

      try {
        event.preventDefault();

        // Check if canvas exists
        if (!roiCanvas) {
          console.error("ROI Canvas element not found in handleRoiMouseDown");
          return;
        }

        // Check if context exists
        if (!ctx) {
          console.error("Canvas context not found in handleRoiMouseDown");
          return;
        }

        isDrawing = true;

        // Get position relative to canvas
        const rect = roiCanvas.getBoundingClientRect();
        console.log("Canvas rect:", rect);

        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        console.log("Mouse position relative to canvas:", x, y);

        // Add point to current polygon
        currentPolygon.push([x, y]);
        console.log(`Point added at ${x}, ${y}`);
        console.log("Current polygon now has", currentPolygon.length, "points");

        // Draw polygons
        drawPolygons();

        // Draw a temporary circle to show where the point was added
        ctx.beginPath();
        ctx.arc(x, y, 8, 0, Math.PI * 2);
        ctx.fillStyle = "rgba(255, 255, 0, 0.5)";
        ctx.fill();

        // Fade out the indicator after a short delay
        setTimeout(() => {
          drawPolygons();
        }, 300);
      } catch (error) {
        console.error("Error in handleRoiMouseDown:", error);
      }
    }

    // ROI Mouse up handler
    function handleRoiMouseUp(event) {
      isDrawing = false;
    }

    // ROI Mouse move handler
    function handleRoiMouseMove(event) {
      if (currentPolygon.length > 0) {
        // Get position relative to canvas
        const rect = roiCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Draw preview line
        drawPolygonsWithPreview(x, y);

        // If actively drawing, add points
        if (isDrawing) {
          currentPolygon.push([x, y]);
          drawPolygons();
        }
      }
    }

    // ROI Double click handler
    function handleRoiDoubleClick(event) {
      console.log("ROI canvas double click detected", event);
      event.preventDefault();
      event.stopPropagation();

      try {
        console.log("Current polygon points:", currentPolygon.length, currentPolygon);

        if (currentPolygon.length > 2) {
          console.log("Closing polygon with", currentPolygon.length, "points");

          // Create a copy of the current polygon
          const completedPolygon = [...currentPolygon];

          // Add the first point again to close the polygon
          completedPolygon.push(completedPolygon[0]);

          // Add to the list of polygons
          polygons.push(completedPolygon);
          console.log("Polygon completed and added to polygons list. Total polygons:", polygons.length);

          // Reset for next polygon
          currentPolygon = [];

          // Redraw polygons
          drawPolygons();

          // Add a direct click handler to close the polygon manually if double-click doesn't work
          const closePolygonBtn = document.createElement('button');
          closePolygonBtn.textContent = 'Close Polygon';
          closePolygonBtn.style.position = 'absolute';
          closePolygonBtn.style.bottom = '50px';
          closePolygonBtn.style.left = '10px';
          closePolygonBtn.style.zIndex = '30';
          closePolygonBtn.className = 'btn btn-primary';

          closePolygonBtn.addEventListener('click', function() {
            if (currentPolygon.length > 2) {
              // Close the polygon
              currentPolygon.push(currentPolygon[0]);

              // Add to list of polygons
              polygons.push([...currentPolygon]);
              console.log("Polygon closed manually with", currentPolygon.length, "points");

              // Reset for next polygon
              currentPolygon = [];

              // Redraw polygons
              drawPolygons();

              // Remove this button
              this.remove();
            }
          });

          // Add the button to the ROI container
          const roiContainer = document.getElementById('roi-container');
          if (roiContainer && currentPolygon.length > 0) {
            roiContainer.appendChild(closePolygonBtn);
          }
        } else {
          console.log("Not enough points to close polygon, need at least 3 points");
        }
      } catch (error) {
        console.error("Error in handleRoiDoubleClick:", error);
      }
    }

    // Draw polygons with preview line
    function drawPolygonsWithPreview(mouseX, mouseY) {
      if (!ctx || !roiCanvas) return;

      // Clear canvas
      ctx.clearRect(0, 0, roiCanvas.width, roiCanvas.height);

      // Draw completed polygons
      for (let i = 0; i < polygons.length; i++) {
        const polygon = polygons[i];

        // Draw polygon outline
        ctx.beginPath();
        for (let j = 0; j < polygon.length; j++) {
          const [x, y] = polygon[j];
          if (j === 0) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.strokeStyle = "red";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw vertices
        for (let j = 0; j < polygon.length; j++) {
          const [x, y] = polygon[j];
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fillStyle = "red";
          ctx.fill();
        }

        // Draw label
        const centroid = getCentroid(polygon);
        ctx.fillStyle = "red";
        ctx.font = "16px Arial";
        ctx.fillText(`ROI:${i}`, centroid[0] + 5, centroid[1] - 5);
      }

      // Draw current polygon with preview line
      if (currentPolygon.length > 0) {
        // Draw current polygon
        ctx.beginPath();
        for (let i = 0; i < currentPolygon.length; i++) {
          const [x, y] = currentPolygon[i];
          if (i === 0) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }

        // Add preview line to mouse position
        ctx.lineTo(mouseX, mouseY);

        ctx.strokeStyle = "blue";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw vertices
        for (let i = 0; i < currentPolygon.length; i++) {
          const [x, y] = currentPolygon[i];
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fillStyle = "blue";
          ctx.fill();
        }
      }
    }

    // Draw all polygons on the canvas
    function drawPolygons() {
      if (!ctx || !roiCanvas) return;

      ctx.clearRect(0, 0, roiCanvas.width, roiCanvas.height); // Clear the canvas

      // Draw all completed polygons
      for (let i = 0; i < polygons.length; i++) {
        const polygon = polygons[i];
        ctx.beginPath();

        for (let j = 0; j < polygon.length; j++) {
          const [x, y] = polygon[j];
          if (j === 0) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }

        ctx.closePath();
        ctx.strokeStyle = "red";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw points for each vertex
        for (let j = 0; j < polygon.length; j++) {
          const [x, y] = polygon[j];
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fillStyle = "red";
          ctx.fill();
        }

        // Calculate the centroid of the polygon to place the label
        const centroid = getCentroid(polygon);
        ctx.fillStyle = "red";
        ctx.font = "16px Arial";
        ctx.fillText(`ROI:${i}`, centroid[0] + 5, centroid[1] - 5);
      }

      // Draw the current polygon being drawn
      if (currentPolygon.length > 0) {
        ctx.beginPath();

        for (let i = 0; i < currentPolygon.length; i++) {
          const [x, y] = currentPolygon[i];
          if (i === 0) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }

        ctx.strokeStyle = "blue";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw points for each vertex
        for (let i = 0; i < currentPolygon.length; i++) {
          const [x, y] = currentPolygon[i];
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fillStyle = "blue";
          ctx.fill();
        }
      }
    }

    // Calculate the centroid of a polygon
    function getCentroid(polygon) {
      let xSum = 0;
      let ySum = 0;

      for (let i = 0; i < polygon.length; i++) {
        xSum += polygon[i][0];
        ySum += polygon[i][1];
      }

      return [xSum / polygon.length, ySum / polygon.length];
    }

    // Load existing ROI coordinates
    function loadExistingROI(cameraName) {
      fetch('/crowd_detection/get-cameras')
        .then(response => response.json())
        .then(cameras => {
          if (cameras[cameraName] && cameras[cameraName][1] && cameras[cameraName][1].length > 0) {
            polygons = cameras[cameraName][1];
            drawPolygons();

            // Show save button if there are existing polygons
            if (polygons.length > 0) {
              saveRoiBtn.style.display = 'block';
            }
          }
        })
        .catch(error => console.error('Error loading ROI coordinates:', error));
    }

    // Clear all polygons
    function clearRoiPolygons() {
      console.log("Clearing all polygons");

      // Clear polygons
      polygons = [];
      currentPolygon = [];

      // Clear canvas
      if (ctx && roiCanvas) {
        ctx.clearRect(0, 0, roiCanvas.width, roiCanvas.height);
      }

      console.log('All polygons cleared');
    }

    // Save ROI coordinates to the server
    function saveRoiCoordinates() {
      try {
        const currentCameraName = cameraName.textContent;

        console.log("Saving ROI coordinates for camera:", currentCameraName);
        console.log("Polygons:", polygons);

        // Check if there are any polygons to save
        if (polygons.length === 0) {
          console.log("No polygons to save");
          alert("Please draw at least one polygon before saving.");
          return;
        }

        // If there's an open polygon, ask if the user wants to close it
        if (currentPolygon.length > 2) {
          if (confirm("You have an open polygon. Do you want to close it before saving?")) {
            // Close the polygon
            currentPolygon.push(currentPolygon[0]);

            // Add to list of polygons
            polygons.push([...currentPolygon]);
            console.log("Polygon closed before saving with", currentPolygon.length, "points");

            // Reset for next polygon
            currentPolygon = [];

            // Redraw polygons
            drawPolygons();
          }
        }

        // Send coordinates to server
        fetch('/crowd_detection/send-coordinates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            cameraName: currentCameraName,
            coordinates: polygons
          })
        })
        .then(response => response.json())
        .then(data => {
          console.log('ROI coordinates saved:', data);

          // Show success message
          alert('Focus area saved successfully!');

          // Exit drawing mode
          exitRoiDrawingMode();
        })
        .catch(error => {
          console.error('Error saving ROI coordinates:', error);
          alert('Error saving focus area. Please try again.');
        });
      } catch (error) {
        console.error("Error in saveRoiCoordinates:", error);
        alert('An error occurred while saving. Please try again.');
      }
    }

    function setupWebSocket(cameraId) {
      // Initialize WebSocket connection with proper protocol
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsHost = window.location.host;
      const wsUrl = `${wsProtocol}//${wsHost}/crowd_detection/ws`;

      console.log('Connecting to WebSocket at:', wsUrl);

      const ws = new WebSocket(wsUrl);
      ws.binaryType = 'arraybuffer';

      let lastMetadata = null;

      ws.onopen = () => {
        console.log('WebSocket connection established');
      };

      ws.onmessage = (event) => {
        console.log("WebSocket message received:", typeof event.data);

        if (typeof event.data === 'string') {
          console.log("Metadata received:", event.data);
          lastMetadata = event.data.split(':');
        } else if (event.data instanceof ArrayBuffer) {
          console.log("Binary data received, length:", event.data.byteLength);

          if (lastMetadata) {
            const [msgCameraId, cameraName, count, roiCountsStr] = lastMetadata;
            console.log("Processing camera:", msgCameraId, cameraName, "Count:", count);

            // Only update if this is the camera we're viewing
            if (msgCameraId === cameraId) {
              try {
                // Convert ArrayBuffer to base64
                const binary = [];
                const bytes = new Uint8Array(event.data);
                const len = bytes.byteLength;
                for (let i = 0; i < len; i++) {
                  binary.push(String.fromCharCode(bytes[i]));
                }
                const base64Image = window.btoa(binary.join(''));
                const dataUrl = `data:image/jpeg;base64,${base64Image}`;

                // Update camera feed
                const cameraFeed = document.getElementById('camera-feed');
                if (cameraFeed) {
                  // Set image properties for proper display
                  cameraFeed.style.width = '100%';
                  cameraFeed.style.height = '100%';
                  cameraFeed.style.objectFit = 'cover';
                  cameraFeed.style.position = 'absolute';
                  cameraFeed.style.top = '0';
                  cameraFeed.style.left = '0';
                  cameraFeed.style.right = '0';
                  cameraFeed.style.bottom = '0';
                  cameraFeed.style.zIndex = '1';

                  cameraFeed.onload = function() {
                    console.log("Image loaded successfully");
                  };

                  cameraFeed.onerror = function() {
                    console.error("Failed to load image for camera:", cameraId);
                    this.onerror = null; // Prevent infinite error loop
                    this.src = ''; // Clear the source

                    // Show error placeholder
                    const webcamContainer = document.getElementById('webcam-container');
                    if (webcamContainer) {
                      webcamContainer.innerHTML = `
                        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #0d1b42;">
                          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ff5252" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                          </svg>
                          <p style="margin-top: 10px; color: white;">Error loading camera feed</p>
                        </div>
                      `;
                    }
                  };

                  cameraFeed.src = dataUrl;
                }

                // Update people count
                const peopleCount = document.getElementById('people-count');
                if (peopleCount) {
                  peopleCount.textContent = count;
                }

                // Update ROI counts
                try {
                  const roiCounts = JSON.parse(roiCountsStr);
                  if (roiCounts && roiCounts.length > 0) {
                    const roiCountsElement = document.getElementById('roi-counts');
                    if (roiCountsElement) {
                      roiCountsElement.innerHTML = '';

                      roiCounts.forEach((count, index) => {
                        const countClass = count > 10 ? 'red-count' : count > 5 ? 'amber-count' : 'green-count';
                        roiCountsElement.innerHTML += `
                          <div class="count-item">
                            <div class="count-label">Region ${index + 1}</div>
                            <div class="count-value ${countClass}">${count}</div>
                          </div>
                        `;
                      });
                    }
                  }
                } catch (error) {
                  console.warn('Failed to parse ROI counts:', error);
                }

                // Update last updated time
                const lastUpdated = document.getElementById('last-updated');
                if (lastUpdated) {
                  lastUpdated.textContent = 'Updated just now';
                }

                // Update camera status
                const cameraStatus = document.getElementById('camera-status');
                if (cameraStatus) {
                  cameraStatus.textContent = 'Live';
                  cameraStatus.className = 'status-badge status-active';
                }
              } catch (error) {
                console.error("Error processing camera frame:", error);
              }
            }

            lastMetadata = null;
          } else {
            console.warn("Received binary data without metadata");
          }
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        console.log("WebSocket connection error. Will try to reconnect automatically.");
      };

      ws.onclose = (event) => {
        console.log('WebSocket connection closed. Code:', event.code, 'Reason:', event.reason);

        // Try to reconnect if the connection was closed unexpectedly
        console.log("Attempting to reconnect WebSocket in 2 seconds...");
        setTimeout(() => {
          setupWebSocket(cameraId);
        }, 2000);
      };
    }

    function formatTimeAgo(date) {
      const now = new Date();
      const diffMs = now - date;
      const diffSec = Math.floor(diffMs / 1000);

      if (diffSec < 60) return 'just now';
      if (diffSec < 120) return '1 minute ago';
      if (diffSec < 3600) return `${Math.floor(diffSec / 60)} minutes ago`;
      if (diffSec < 7200) return '1 hour ago';
      if (diffSec < 86400) return `${Math.floor(diffSec / 3600)} hours ago`;

      return date.toLocaleDateString();
    }

    // Function to reset the camera feed to offline state
    function resetCameraFeedToOffline() {
      // Reset the camera feed image
      const webcamContainer = document.getElementById('webcam-container');
      if (webcamContainer) {
        webcamContainer.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #f0f0f0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#ff9800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
              <circle cx="12" cy="13" r="4"></circle>
            </svg>
            <h3 style="margin-top: 20px; color: #333;">Monitoring not active</h3>
            <p style="margin-top: 10px; color: #666;">Please start monitoring from the dashboard to view the camera feed.</p>
            <button id="startMonitoringBtn" style="margin-top: 20px; padding: 10px 20px; background-color: #1e88e5; color: white; border: none; border-radius: 4px; cursor: pointer;">Start Monitoring</button>
          </div>
        `;
      }

      // Update status badge
      const cameraStatus = document.getElementById('camera-status');
      if (cameraStatus) {
        cameraStatus.textContent = 'Offline';
        cameraStatus.className = 'status-badge status-offline';
      }

      // Reset metrics
      const peopleCount = document.getElementById('people-count');
      if (peopleCount) {
        peopleCount.textContent = '0';
      }

      const lastUpdated = document.getElementById('last-updated');
      if (lastUpdated) {
        lastUpdated.textContent = 'Monitoring not active';
      }

      const roiCounts = document.getElementById('roi-counts');
      if (roiCounts) {
        roiCounts.innerHTML = '';
      }

      // Add event listener to the Start Monitoring button
      setTimeout(() => {
        const startMonitoringBtn = document.getElementById('startMonitoringBtn');
        if (startMonitoringBtn) {
          startMonitoringBtn.addEventListener('click', async function() {
            try {
              const response = await fetch('/crowd_detection/start-stream', { method: 'POST' });
              if (response.ok) {
                console.log("Stream started successfully from expanded view");
                // Store monitoring state in localStorage
                localStorage.setItem('crowdMonitoringActive', 'true');
                // Reload the page to apply the new monitoring state
                window.location.reload();
              } else {
                console.error("Failed to start stream");
                alert("Failed to start monitoring. Please try again.");
              }
            } catch (error) {
              console.error("Error starting stream:", error);
              alert("Error starting monitoring. Please try again.");
            }
          });
        }
      }, 100);
    }
  </script>
</body>
</html>
