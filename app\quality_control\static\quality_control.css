* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f4f4f4;
}

.container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: auto; /* Adjust rows dynamically */
  gap: 10px;
  width: 100vw; /* Full viewport width */
  height: 100vh; /* Full viewport height */
  max-width: 100%;
  max-height: 100%;
  background: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  box-sizing: border-box; /* Ensure padding and border are included in the width/height */
  overflow: hidden; /* Prevent content overflow */
}



.sidebar {
  background-color: #202020;
  color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.logo {
  /* font-size: 1.5rem; */
  margin-top: 30px;
  text-align: center;
  width: 100%;
}
.logo img{
  width: 100%;
  max-width: 150px;
  height: auto;
}

.menu-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-radius: 10px;  
}

.menu-btn:hover {
  background-color: #333;

}

.homepage-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: start;
  border-radius: 10px;
}

.homepage-btn:hover {
  background-color: #333;
}

.addcamera-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: start;
  border-radius: 10px;
}

.addcamera-btn:hover {
  background-color: #333;
}

.main {
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-height: 100dvh;
  overflow-y: auto;
}

.webcam-section {
  text-align: center;
  padding: 10px;
  background-color: #f0f0f0;
  margin-bottom: 20px;
}

.video-grid {
  display: flex;
  flex-wrap: wrap;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.video-box {
  background-color: #ddd;
  height: 100px;
  border-radius: 8px;
}

.live-observations {
  background-color: #f8f8f8;
  padding: 20px;
}

.live-observations h2 {
  margin-bottom: 20px;
  text-align: center;
}

.live-observations ul {
  list-style: none;
}

.live-observations li {
  margin-bottom: 10px;
  padding: 8px;
  background: #eee;
  border-radius: 5px;
}

.camera-feed{
  width: 400px;
  max-width: 80%;
  height: auto;
  max-height: 400px;
}
.camera-feed img{
  object-fit: contain;
  height: 100%;
  width: 100%;
}



.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
}
.modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
}
.close-btn {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  float: right;
}

.close-btn:hover,
.close-btn:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}


/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 30px;
  border: 1px solid #888;
  width: 60%;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-content h2 {
  font-size: 1.8em;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.close-btn {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

.close-btn:hover {
  color: #000;
}

.camera-list {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.camera-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  border-bottom: 1px solid #ccc;
  background-color: #fff;
  border-radius: 5px;
  margin-bottom: 8px;
}

.camera-item:last-child {
  border-bottom: none;
}

.camera-item button {
  background-color: #f44336;
  color: white;
  padding: 8px 12px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.camera-item button:hover {
  background-color: #d32f2f;
}

#cameraForm input {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 16px;
}

#cameraForm button {
  padding: 12px 20px;
  background-color: #2a2238;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  margin-top: 15px;
}

#cameraForm button:hover {
  background-color: #e60da5;
}

#cameraList {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f5f5f5;
}


.waterlevel-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: start;
  border-radius: 10px;
}

.waterlevel-btn:hover {
  background-color: #333;
}

/* Water Level Modal Styling */
.waterlevel-modal {
font-family: "Arial", sans-serif;
width: 400px;
background-color: white;
border-radius: 10px;
padding: 20px;
text-align: center;
box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}

.waterlevel-modal .modal-title {
font-size: 20px;
font-weight: bold;
margin-bottom: 15px;
}

.waterlevel-modal .modal-body {
font-size: 16px;
margin-bottom: 20px;
}

.waterlevel-modal input {
width: 80%;
padding: 10px;
margin-top: 10px;
border: 1px solid #ccc;
border-radius: 5px;
}

.waterlevel-modal .modal-footer {
display: flex;
justify-content: space-around;
margin-top: 20px;
}

.submit-btn, .cancel-btn {
padding: 10px 15px;
font-size: 14px;
border: none;
border-radius: 5px;
cursor: pointer;
}

.submit-btn {
background-color: #28a745;
color: white;
}

.cancel-btn {
background-color: #dc3545;
color: white;
}
