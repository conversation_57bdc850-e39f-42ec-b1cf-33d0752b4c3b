<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VigilanteEye Dashboard</title>
    <link rel="stylesheet" href="/static/dashboard.css">
</head>
<body>
    <!-- Header with logo and company name -->
    <div class="header">
        <div class="logo-container">
            <img src="/static/logo.png" alt="Logo" class="logo">
            <span class="company-name">VigilanteEye</span>
        </div>
        <!-- Logout button -->
        <a href="/logout" class="logout-btn">Logout</a>
    </div>

    <!-- Main content with clickable boxes -->
    <div class="container">
        <a href="http://localhost:8004/" class="box quality" target="_blank">Quality Control</a>
        <a href="http://localhost:8003/" class="box helmet" target="_blank">Helmet Detection</a>
        <a href="http://localhost:8002/" class="box crowd" target="_blank">Crowd Detection</a>
        <a href="http://localhost:8001/" class="box face" target="_blank">Face Recognition</a>
    </div>

    <!-- Service Status Section -->
    <div class="status-container">
        <h3>Service Status</h3>
        <div class="status-grid">
            <div class="status-item">
                <span class="service-name">Auth Service</span>
                <span class="status-indicator" id="auth-status">Checking...</span>
            </div>
            <div class="status-item">
                <span class="service-name">Face Recognition</span>
                <span class="status-indicator" id="face-status">Checking...</span>
            </div>
            <div class="status-item">
                <span class="service-name">Crowd Detection</span>
                <span class="status-indicator" id="crowd-status">Checking...</span>
            </div>
            <div class="status-item">
                <span class="service-name">Helmet Detection</span>
                <span class="status-indicator" id="helmet-status">Checking...</span>
            </div>
            <div class="status-item">
                <span class="service-name">Quality Control</span>
                <span class="status-indicator" id="quality-status">Checking...</span>
            </div>
        </div>
    </div>

    <script>
        // Check service health status
        async function checkServiceHealth(url, elementId) {
            try {
                const response = await fetch(url + '/health');
                const data = await response.json();
                const element = document.getElementById(elementId);
                
                if (response.ok && data.status === 'healthy') {
                    element.textContent = 'Online';
                    element.className = 'status-indicator online';
                } else {
                    element.textContent = 'Offline';
                    element.className = 'status-indicator offline';
                }
            } catch (error) {
                const element = document.getElementById(elementId);
                element.textContent = 'Offline';
                element.className = 'status-indicator offline';
            }
        }

        // Check all services on page load
        window.addEventListener('load', function() {
            checkServiceHealth('http://localhost:8000', 'auth-status');
            checkServiceHealth('http://localhost:8001', 'face-status');
            checkServiceHealth('http://localhost:8002', 'crowd-status');
            checkServiceHealth('http://localhost:8003', 'helmet-status');
            checkServiceHealth('http://localhost:8004', 'quality-status');
        });

        // Refresh status every 30 seconds
        setInterval(function() {
            checkServiceHealth('http://localhost:8000', 'auth-status');
            checkServiceHealth('http://localhost:8001', 'face-status');
            checkServiceHealth('http://localhost:8002', 'crowd-status');
            checkServiceHealth('http://localhost:8003', 'helmet-status');
            checkServiceHealth('http://localhost:8004', 'quality-status');
        }, 30000);
    </script>
</body>
</html>
