import os
import shutil
from pathlib import Path

def check_images():
    """
    Check the images in the static/images directory and copy any missing images from cropped_faces.
    """
    # Ensure the static/images directory exists
    os.makedirs("static/images", exist_ok=True)
    
    # Get all cropped face images
    cropped_faces_dir = Path("cropped_faces")
    if not cropped_faces_dir.exists():
        print("Cropped faces directory not found.")
        return
    
    # Get all images in the static/images directory
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]
    
    print("Images in static/images directory:")
    for img in static_images:
        print(f"  - {img}")
    
    print("\nImages in cropped_faces directory:")
    for img in cropped_faces_dir.glob("*.jpg"):
        print(f"  - {img.name}")
        
    # Copy all images from cropped_faces to static/images
    print("\nCopying all images from cropped_faces to static/images...")
    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        print(f"  Copying {cropped_face.name} to static/images directory...")
        shutil.copy(cropped_face, static_images_dir / cropped_face.name)
    
    print("\nImages in static/images directory after copying:")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]
    for img in static_images:
        print(f"  - {img}")

if __name__ == "__main__":
    check_images()
