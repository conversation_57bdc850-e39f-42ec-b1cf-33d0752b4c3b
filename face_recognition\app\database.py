from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# Face Recognition database URL
SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
)

# Auth database URL (same as main database for now)
AUTH_SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.AUTH_DB_USER}:{settings.AUTH_DB_PASSWORD}@{settings.AUTH_DB_HOST}:{settings.AUTH_DB_PORT}/{settings.AUTH_DB_NAME}"
)

# Create engines
engine = create_engine(SQLALCHEMY_DATABASE_URL)
auth_engine = create_engine(AUTH_SQLALCHEMY_DATABASE_URL)

# Create session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AuthSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=auth_engine)

# Create declarative bases
Base = declarative_base()
AuthBase = declarative_base()

def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_auth_db():
    """Dependency to get auth database session"""
    db = AuthSessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Create database tables"""
    Base.metadata.create_all(bind=engine)

def create_auth_tables():
    """Create auth database tables"""
    AuthBase.metadata.create_all(bind=auth_engine)
