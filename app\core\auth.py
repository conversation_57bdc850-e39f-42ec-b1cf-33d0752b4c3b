from datetime import datetime, timed<PERSON>ta
from typing import Optional
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON><PERSON>Bearer
from passlib.context import <PERSON>pt<PERSON>ontext
from sqlalchemy.orm import Session

# Import models and config
from app.core.models import User, TokenData
from app.core.config import settings
from app.core.database import get_auth_db

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Functions for password handling
def verify_password(plain_password, hashed_password):
    print(f"Verifying password: plain_password length={len(plain_password)}")
    print(f"Hashed password: {hashed_password}")
    result = pwd_context.verify(plain_password, hashed_password)
    print(f"Password verification result: {result}")
    return result

def get_password_hash(password):
    return pwd_context.hash(password)

# Database functions
def get_user(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()

def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, username: str, email: str, password: str, full_name: str = None, role: str = "user"):
    print(f"Creating user with username: {username}, email: {email}, role: {role}")
    print(f"Password to hash: {password}")

    hashed_password = get_password_hash(password)
    print(f"Generated hashed password: {hashed_password}")

    db_user = User(
        username=username,
        email=email,
        full_name=full_name,
        hashed_password=hashed_password,
        role=role
    )

    try:
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        print(f"User created successfully: {db_user.id}, {db_user.username}")
        return db_user
    except Exception as e:
        print(f"Error creating user: {str(e)}")
        db.rollback()
        raise

def authenticate_user(db: Session, username: str, password: str):
    print(f"Authenticating user: {username}")
    user = get_user(db, username)
    if not user:
        print(f"User {username} not found in database")
        return False

    print(f"User found: {user.username}, {user.email}")
    print(f"Verifying password for user {username}")

    # Debug: print password hash for comparison
    print(f"Stored hashed password: {user.hashed_password}")

    if not verify_password(password, user.hashed_password):
        print(f"Password verification failed for user {username}")
        return False

    print(f"Password verification successful for user {username}")
    return user

# Token functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_auth_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception

    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
