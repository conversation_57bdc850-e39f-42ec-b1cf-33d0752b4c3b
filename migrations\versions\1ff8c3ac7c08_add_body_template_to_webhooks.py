"""add_body_template_to_webhooks

Revision ID: 1ff8c3ac7c08
Revises: 3b486235e84f
Create Date: 2025-05-26 10:46:31.455369

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ff8c3ac7c08'
down_revision: Union[str, None] = '3b486235e84f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add body_template column to webhooks table
    op.add_column('webhooks', sa.Column('body_template', sa.JSON(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove body_template column from webhooks table
    op.drop_column('webhooks', 'body_template')
