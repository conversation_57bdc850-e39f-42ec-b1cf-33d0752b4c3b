from fastapi import APIRouter, WebSocket, WebSocketDisconnect, status, Request
from fastapi.responses import StreamingResponse, RedirectResponse
from fastapi import FastAPI, Form, File, UploadFile, Depends, HTTPException, Body
from typing import List
import asyncio
import cv2
import os
import datetime
import numpy as np
from sqlalchemy.orm import Session
from database import SessionLocal
from models import Encoding, User, Unknown
from utils import generate_encoding, calculate_cosine_similarity, crop_face, get_username_from_id, load_cameras, save_cameras
import models
from fastapi import Request
from fastapi.templating import Jinja2Templates
import logging
from fastapi.responses import JSONResponse
from huggingface_hub import hf_hub_download

# Set up logging
logger = logging.getLogger(__name__)
from fastapi.datastructures import UploadFile as FastAPIUploadFile
from starlette.datastructures import FormData
from tester import FaceDetection
from services.qdrant_service import QdrantService
from fastapi import Request, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, Integer, cast
from typing import Optional, Dict, Any, List
from datetime import timezone
from pydantic import BaseModel


try:
    qdrant_service = QdrantService()
    if not qdrant_service.available:
        logger.warning("Qdrant service is not available. Vector search functionality will be disabled.")
except Exception as e:
    logger.error(f"Failed to initialize Qdrant service: {e}")
    qdrant_service = None

logging.basicConfig(level=logging.DEBUG)

# Camera configuration is now stored in the database

router = APIRouter()

templates = Jinja2Templates(directory="face_recognition/templates")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.get("/face_recognition")
async def show_face_recognition_page(request: Request):
    # Get user role from request state (set by middleware)
    user_role = getattr(request.state, "user", {}).get("role", "user")
    return templates.TemplateResponse(
        "face_recognition.html",
        {"request": request, "user_role": user_role}
    )

@router.get("/registration")
async def show_registration_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})


@router.post("/register/")
async def register(
    request: Request,
    db: Session = Depends(get_db)
):
    # Manually parse form with increased size limit (5MB)
    form_data = await request.form(max_part_size=5 * 1024 * 1024)

    # Extract form fields
    username = form_data.get("username")
    email = form_data.get("email")
    image_file = form_data.get("image_file")

    if not username or not email or not image_file:
        raise HTTPException(status_code=400, detail="Missing required fields")

    # Check if user exists
    db_user = db.query(models.User).filter(models.User.username == username).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Username already taken")

    db_email = db.query(models.User).filter(models.User.email == email).first()
    if db_email:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Continue with the rest of the function as before
    image_extension = os.path.splitext(image_file.filename)[1] or ".jpg"
    image_filename = f"{username}.jpg"
    image_path = f"static/images/{image_filename}"

    # Ensure the directory exists
    os.makedirs(os.path.dirname(image_path), exist_ok=True)

    # Read the image file content
    image_content = await image_file.read()

    # Save the original image
    with open(image_path, "wb") as buffer:
        buffer.write(image_content)

    print(f"Saved original image to {image_path}")

    # Crop the face and save the cropped image
    cropped_image_path = f"./cropped_faces/{username}.jpg"
    os.makedirs(os.path.dirname(cropped_image_path), exist_ok=True)

    # Convert the image content to a format that crop_face can process
    nparr = np.frombuffer(image_content, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

    # Crop the face
    cropped_face = crop_face(img)
    if cropped_face is None:
        raise HTTPException(status_code=400, detail="No face detected in the image.")

    # Save the cropped face
    cv2.imwrite(cropped_image_path, cropped_face)

    # Save user to the database
    db_user = models.User(username=username, email=email)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Generate face encoding
    encoding = generate_encoding(face_image=cropped_face)

    # Save the image details to the database
    db_image = models.Image(filename=image_filename, image_url=cropped_image_path, id=db_user.user_id)
    db.add(db_image)
    db.commit()
    db.refresh(db_image)

    # Save the encoding details to the database
    encoding_list = encoding.tolist()
    db_encoding = models.Encoding(image_id=db_image.id, encoding=encoding_list, user_id=db_user.user_id)
    db.add(db_encoding)
    db.commit()

    print("Adding face embedding to Qdrant...")

    # First, delete any existing embeddings for this user ID
    try:
        # Make sure to convert user_id to string for Qdrant
        user_id_str = str(db_user.user_id)
        qdrant_service.delete_face_embedding(user_id_str)
        print(f"[INFO] Deleted any existing embeddings for new user {username} (ID: {db_user.user_id}) from Qdrant")
    except Exception as delete_error:
        print(f"[WARNING] Error deleting existing embeddings: {delete_error}")
        import traceback
        print(f"[ERROR] Traceback: {traceback.format_exc()}")

    # Then add the new embedding
    try:
        # Make sure to convert user_id to string for Qdrant
        user_id_str = str(db_user.user_id)
        success = qdrant_service.add_face_embedding(
            user_id=user_id_str,  # Always convert to string
            username=username,
            encoding=encoding
        )

        if success:
            print(f"[INFO] Added face embedding to Qdrant for new user {username} (ID: {db_user.user_id}, String ID: {user_id_str})")
        else:
            print(f"[WARNING] Failed to add face embedding to Qdrant for new user {username} (ID: {db_user.user_id}, String ID: {user_id_str})")
    except Exception as add_error:
        print(f"[ERROR] Failed to add face embedding to Qdrant: {add_error}")
        import traceback
        print(f"[ERROR] Traceback: {traceback.format_exc()}")

    return {"message": "User registered successfully", "user": username, "image_path": cropped_image_path}

@router.get("/available_cameras")
async def available_cameras():
    available_cameras = get_cameras()
    print
    return {"cameras": available_cameras}

@router.get("/get_attendance", response_class=JSONResponse)
async def get_attendance(db: Session = Depends(get_db)):
    """Fetch attendance records"""
    attendance_records = db.query(models.Attendance).all()
    return [
        {
            "id": record.id,
            "user_id": record.user_id,
            "timestamp": record.timestamp,
            "camera_name": record.camera_name or "System Camera"
        }
        for record in attendance_records
    ]
